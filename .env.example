# Cloudflare R2 Configuration
# Copy this file to .env and fill in your actual values

# R2 API Credentials
# Get these from Cloudflare Dashboard -> R2 Object Storage -> Manage R2 API tokens
R2_ACCESS_KEY_ID=your_r2_access_key_id_here
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key_here

# Cloudflare Account ID
# Found in the right sidebar of your Cloudflare dashboard
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id_here

# R2 Bucket Name
# The name of your R2 bucket where documents will be stored
R2_BUCKET_NAME=your_r2_bucket_name_here

# Optional: Additional Configuration
# Uncomment and modify as needed

# S3FS Cache Directory (default: /tmp)
# S3FS_CACHE_DIR=/tmp/s3fs-cache

# Application Configuration
# SERVER_PORT=8080
# LOG_LEVEL=info

# Docker Configuration
# COMPOSE_PROJECT_NAME=api-upload
# COMPOSE_FILE=docker-compose.yml
