// Mock implementation of cloudflare:workers for testing

export class DurableObject<Env = any> {
  constructor(public ctx: any, public env: Env) {}
}

export class WorkflowEntrypoint {
  constructor(public ctx: any, public env: any) {}

  async run(event: WorkflowEvent, step: WorkflowStep): Promise<any> {
    // Mock implementation
    return {};
  }
}

export interface WorkflowEvent {
  type: string;
  payload?: any;
}

export interface WorkflowStep {
  do<T>(name: string, fn: () => Promise<T>): Promise<T>;
  sleep(duration: string | number): Promise<void>;
  sleepUntil(timestamp: Date | number): Promise<void>;
}

// Mock WorkflowStep implementation
export const createMockWorkflowStep = (): WorkflowStep => ({
  async do<T>(name: string, fn: () => Promise<T>): Promise<T> {
    return await fn();
  },
  async sleep(duration: string | number): Promise<void> {
    // Mock implementation - don't actually sleep in tests
    return Promise.resolve();
  },
  async sleepUntil(timestamp: Date | number): Promise<void> {
    // Mock implementation - don't actually sleep in tests
    return Promise.resolve();
  },
});
