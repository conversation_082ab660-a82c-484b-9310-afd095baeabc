/**
 * @vitest-environment node
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { CreateRequest } from '../src/requests/create';
import { CompleteRequest } from '../src/requests/complete';
import { AbortRequest } from '../src/requests/abort';
import { StatusRequest } from '../src/requests/status';
import { UploadRecord } from '../src/lib/UploadRecord';

// Mock implementations for testing
class MockR2Bucket {
  async createMultipartUpload(key: string, options?: any) {
    return { key, uploadId: 'test-upload-123' };
  }

  resumeMultipartUpload(key: string, uploadId: string) {
    return {
      complete: async (parts: any[]) => ({
        key,
        httpEtag: '"test-etag"'
      }),
      abort: async () => {}
    };
  }
}



class MockD1Database {
  private data: Map<string, any> = new Map();

  prepare(query: string) {
    return {
      bind: (...params: any[]) => ({
        run: async () => ({ success: true }),
        first: async () => {
          if (query.includes("SELECT * FROM uploads WHERE json_extract(properties, '$.upload_id') = ?")) {
            const uploadId = params[0];
            return this.data.get(`upload_${uploadId}`) || null;
          }
          return null;
        },
        all: async () => ({
          results: Array.from(this.data.values()).filter(v => v.properties)
        })
      }),
      run: async () => ({ success: true }),
      first: async () => null,
      all: async () => ({ results: [] })
    };
  }

  setTestData(key: string, value: any) {
    this.data.set(key, value);
  }

  clear() {
    this.data.clear();
  }
}

describe('Upload Flow Integration', () => {
  let mockBucket: MockR2Bucket;

  let mockDb: MockD1Database;

  beforeEach(() => {
    mockBucket = new MockR2Bucket();

    mockDb = new MockD1Database();
    mockDb.clear();
  });

  it('should handle complete upload flow', async () => {
    // 1. Create upload
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any);
    const createResponse = await createRequest.execute('test-file.pdf');

    expect(createResponse.status).toBe(200);
    const createData = await createResponse.json();
    expect(createData).toEqual({
      key: 'test-file.pdf',
      uploadId: 'test-upload-123'
    });

    // 2. Check initial status (should be pending)
    mockDb.setTestData('upload_test-upload-123', {
      id: 'test-id',
      properties: '{"upload_id":"test-upload-123","key":"test-file.txt","status":"pending"}',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    });

    const statusRequest = new StatusRequest(mockDb as any);
    const statusResponse = await statusRequest.execute('test-upload-123');

    expect(statusResponse.status).toBe(200);
    const statusData = await statusResponse.json() as any;
    expect(statusData.status).toBe('pending');

    // 3. Complete upload
    const completeRequest = new CompleteRequest(
      mockBucket as any,
      mockDb as any,
      'test-file.txt',
      'test-upload-123'
    );

    const completeResponse = await completeRequest.execute({ parts: [{ partNumber: 1, etag: '"test-etag"' }] });
    expect(completeResponse.status).toBe(200);
  });

  it('should handle upload abort', async () => {
    // Create upload first
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any);
    await createRequest.execute('test-file.pdf');

    // Abort upload
    const abortRequest = new AbortRequest(mockBucket as any, mockDb as any, 'test-file.pdf', 'test-upload-123');
    const abortResponse = await abortRequest.execute();

    expect(abortResponse.status).toBe(204);
  });

  it('should return 404 for non-existent upload status', async () => {
    const statusRequest = new StatusRequest(mockDb as any);
    const statusResponse = await statusRequest.execute('non-existent-upload');

    expect(statusResponse.status).toBe(404);
  });

  it('should create upload with metadata', async () => {
    // Mock bucket to capture the options passed to createMultipartUpload
    let capturedOptions: any = null;
    const mockBucketWithCapture = {
      ...mockBucket,
      createMultipartUpload: async (key: string, options?: any) => {
        capturedOptions = options;
        return { key, uploadId: 'test-upload-with-metadata' };
      }
    };

    const createRequest = new CreateRequest(mockBucketWithCapture as any, mockDb as any);

    // Test with both httpMetadata and customMetadata
    const metadata = {
      httpMetadata: {
        contentType: 'application/pdf',
        contentLanguage: 'en-US'
      },
      customMetadata: {
        'user-id': '12345',
        'project': 'test-project',
        'version': '1.0'
      }
    };

    const createResponse = await createRequest.execute('test-file-with-metadata.pdf', metadata);

    expect(createResponse.status).toBe(200);
    const createData = await createResponse.json();
    expect(createData).toEqual({
      key: 'test-file-with-metadata.pdf',
      uploadId: 'test-upload-with-metadata'
    });

    // Verify that metadata was passed to createMultipartUpload
    expect(capturedOptions).toEqual({
      httpMetadata: {
        contentType: 'application/pdf',
        contentLanguage: 'en-US'
      },
      customMetadata: {
        'user-id': '12345',
        'project': 'test-project',
        'version': '1.0'
      }
    });
  });

  it('should create upload without metadata when none provided', async () => {
    let capturedOptions: any = null;
    const mockBucketWithCapture = {
      ...mockBucket,
      createMultipartUpload: async (key: string, options?: any) => {
        capturedOptions = options;
        return { key, uploadId: 'test-upload-no-metadata' };
      }
    };

    const createRequest = new CreateRequest(mockBucketWithCapture as any, mockDb as any);
    const createResponse = await createRequest.execute('test-file-no-metadata.pdf');

    expect(createResponse.status).toBe(200);

    // Verify that empty options object was passed
    expect(capturedOptions).toEqual({});
  });

  it('should reject invalid file types', async () => {
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any);

    // Test with .txt file
    const txtResponse = await createRequest.execute('invalid-file.txt');
    expect(txtResponse.status).toBe(400);
    const txtError = await txtResponse.text();
    expect(txtError).toBe('Invalid file type. Only PDF and Word DOCX files are allowed.');

    // Test with .jpg file
    const jpgResponse = await createRequest.execute('image.jpg');
    expect(jpgResponse.status).toBe(400);
    const jpgError = await jpgResponse.text();
    expect(jpgError).toBe('Invalid file type. Only PDF and Word DOCX files are allowed.');

    // Test with no extension
    const noExtResponse = await createRequest.execute('noextension');
    expect(noExtResponse.status).toBe(400);
    const noExtError = await noExtResponse.text();
    expect(noExtError).toBe('Invalid file type. Only PDF and Word DOCX files are allowed.');
  });

  it('should accept valid file types', async () => {
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any);

    // Test PDF file
    const pdfResponse = await createRequest.execute('document.pdf');
    expect(pdfResponse.status).toBe(200);
    const pdfData = await pdfResponse.json();
    expect(pdfData.key).toBe('document.pdf');

    // Test DOCX file
    const docxResponse = await createRequest.execute('document.docx');
    expect(docxResponse.status).toBe(200);
    const docxData = await docxResponse.json();
    expect(docxData.key).toBe('document.docx');

    // Test with uppercase extensions
    const upperPdfResponse = await createRequest.execute('DOCUMENT.PDF');
    expect(upperPdfResponse.status).toBe(200);

    const upperDocxResponse = await createRequest.execute('DOCUMENT.DOCX');
    expect(upperDocxResponse.status).toBe(200);
  });

  it('should reject files with invalid MIME types in metadata', async () => {
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any);

    // Test PDF file with wrong MIME type
    const invalidMimeResponse = await createRequest.execute('document.pdf', {
      httpMetadata: {
        contentType: 'text/plain'
      }
    });
    expect(invalidMimeResponse.status).toBe(400);
    const error = await invalidMimeResponse.text();
    expect(error).toBe('Invalid file type. Only PDF and Word DOCX files are allowed.');
  });

  it('should accept files with correct MIME types in metadata', async () => {
    const createRequest = new CreateRequest(mockBucket as any, mockDb as any);

    // Test PDF file with correct MIME type
    const pdfResponse = await createRequest.execute('document.pdf', {
      httpMetadata: {
        contentType: 'application/pdf'
      }
    });
    expect(pdfResponse.status).toBe(200);

    // Test DOCX file with correct MIME type
    const docxResponse = await createRequest.execute('document.docx', {
      httpMetadata: {
        contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      }
    });
    expect(docxResponse.status).toBe(200);
  });
});
