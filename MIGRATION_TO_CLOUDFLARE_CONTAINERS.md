# Migration to @cloudflare/containers

This document describes the migration from the raw Cloudflare Workers container API to the new `@cloudflare/containers` package.

## What Changed

### 1. Package Installation
- Added `@cloudflare/containers` package to dependencies
- This package provides a simplified `Container` class that extends Durable Objects with container-specific functionality

### 2. Compressor Class Migration

#### Before (Raw Container API):
```typescript
import { DurableObject } from 'cloudflare:workers';

export class Compressor extends DurableObject<Env> {
    container: Container;
    monitor?: Promise<unknown>;

    constructor(ctx: DurableObjectState, env: Env) {
        super(ctx, env);
        if (ctx.container === undefined) {
            throw new Error('no container');
        }
        this.container = ctx.container;
        ctx.blockConcurrencyWhile(async () => {
            if (!this.container.running) {
                await this.container.start({
                    env: { /* environment variables */ },
                    entrypoint: ['/server'],
                    enableInternet: false
                });
            }
            this.monitor = this.container.monitor().then(() => {
                console.log('Container exited?');
            });
        });
    }

    async fetch(req: Request): Promise<Response> {
        void this.container.getTcpPort(8080).fetch('http://container/doc', {
            method: 'POST',
            body: req.body
        });
        return await this.container.getTcpPort(8080).fetch('http://container/health');
    }
}
```

#### After (@cloudflare/containers):
```typescript
import { Container } from '@cloudflare/containers';

export class Compressor extends Container {
    // Configure default port for the container
    defaultPort = 8080;
    
    // Set how long the container should stay active without requests
    sleepAfter = "10m";

    // Environment variables to pass to the container
    env = {
        CLOUDFLARE_ACCOUNT_ID: '',
        R2_ACCESS_KEY_ID: '',
        R2_SECRET_ACCESS_KEY: '',
        R2_BUCKET_NAME: ''
    };

    // Custom entrypoint to override container default
    entrypoint = ['/server'];

    // Disable internet access for the container
    enableInternet = false;

    constructor(ctx: DurableObjectState, env: Env) {
        super(ctx, env);
        
        // Set environment variables from the Cloudflare environment
        this.env = {
            CLOUDFLARE_ACCOUNT_ID: env.CLOUDFLARE_ACCOUNT_ID,
            R2_ACCESS_KEY_ID: env.R2_ACCESS_KEY_ID,
            R2_SECRET_ACCESS_KEY: env.R2_SECRET_ACCESS_KEY,
            R2_BUCKET_NAME: env.R2_BUCKET_NAME
        };
    }

    // Lifecycle method called when container starts
    override onStart(): void {
        console.log('Container started!');
    }

    // Lifecycle method called when container shuts down
    override onStop(): void {
        console.log('Container stopped!');
    }

    // Lifecycle method called on errors
    override onError(error: unknown): any {
        console.error('Container error:', error);
        throw error;
    }

    async fetch(req: Request): Promise<Response> {
        // Send document processing request (fire and forget)
        void this.containerFetch('http://container/doc', {
            method: 'POST',
            body: req.body
        });
        
        // Return health check response
        return await this.containerFetch('http://container/health');
    }
}
```

## Key Benefits of the Migration

### 1. Simplified API
- No need to manually manage `ctx.container` or check if it's running
- Automatic container lifecycle management
- Built-in port management with `defaultPort`

### 2. Better Lifecycle Management
- Automatic container startup and monitoring
- Built-in activity timeout with `sleepAfter`
- Lifecycle hooks (`onStart`, `onStop`, `onError`) for custom behavior

### 3. Cleaner Code
- Removed manual container state checking
- Simplified fetch operations with `containerFetch()`
- Automatic activity timeout renewal on requests

### 4. Enhanced Features
- Automatic port waiting with `startAndWaitForPorts()`
- Built-in WebSocket support
- Load balancing utilities (for future use)

## Configuration Options

The new Container class supports several configuration options:

- `defaultPort`: Default port for container communication (8080)
- `sleepAfter`: How long to keep container alive without activity ("10m")
- `env`: Environment variables passed to the container
- `entrypoint`: Custom entrypoint override (['/server'])
- `enableInternet`: Whether to enable internet access (false)

## Backward Compatibility

- The migration maintains full backward compatibility
- All existing API endpoints continue to work unchanged
- The CompressorWorkflow class requires no changes
- All tests pass without modification

## Files Modified

1. `src/lib/Compressor.ts` - Complete migration to new Container class
2. `package.json` - Added @cloudflare/containers dependency

## Files Unchanged

- `src/lib/CompressorWorkflow.ts` - No changes needed
- `wrangler.toml` - Container configuration remains the same
- `src/types.ts` - Type definitions unchanged
- All test files - No changes required

## Testing

All existing tests pass without modification, confirming that the migration maintains full functionality while providing the benefits of the new Container class.
