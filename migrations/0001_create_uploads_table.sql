-- Migration: Create uploads table with JSON properties
-- This table stores the relationship between upload IDs and workflow instance IDs
-- Uses JSON properties for flexible data storage

-- Drop existing table if it exists (for clean migration)
DROP TABLE IF EXISTS uploads;

-- Create uploads table with JSON properties structure
CREATE TABLE uploads (
  id TEXT PRIMARY KEY,
  properties TEXT NOT NULL DEFAULT (json_object()),
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);

-- Create indexes for better query performance using JSON extraction
CREATE INDEX idx_uploads_upload_id ON uploads(json_extract(properties, '$.upload_id'));
CREATE INDEX idx_uploads_workflow_instance_id ON uploads(json_extract(properties, '$.workflow_instance_id'));
CREATE INDEX idx_uploads_status ON uploads(json_extract(properties, '$.status'));
CREATE INDEX idx_uploads_key ON uploads(json_extract(properties, '$.key'));
CREATE INDEX idx_uploads_created_at ON uploads(created_at);
