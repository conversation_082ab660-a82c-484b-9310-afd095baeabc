# API Upload with Cloudflare Database Integration

This project provides a multipart upload API with Cloudflare Workers, R2 storage, Workflows, and D1 database integration to track upload progress and workflow instances.

## Features

- **Multipart Upload**: Create, upload parts, complete, and abort multipart uploads to Cloudflare R2
- **Workflow Integration**: Automatically triggers compression workflows using Cloudflare Workflows
- **Database Tracking**: Stores upload IDs and workflow instance IDs in Cloudflare D1 database using UploadRecord service
- **Status Monitoring**: Query upload status and workflow progress
- **Container Processing**: Uses Durable Objects with containers for file compression
- **R2 Storage Integration**: Direct access to Cloudflare R2 storage via s3fs-fuse mounting
- **Document Conversion**: Built-in Pandoc support for converting between various document formats
- **Docker Support**: Complete containerized solution with R2 and Pandoc integration
- **DevContainer Support**: Ready-to-use development environment with glibc 2.35+ support

## Database Schema

The system uses a D1 database to track uploads with the following schema:

```sql
CREATE TABLE uploads (
  id TEXT PRIMARY KEY,
  properties TEXT NOT NULL DEFAULT (json_object()),
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
```

The `properties` field contains a JSON object with the following structure:
```json
{
  "upload_id": "string",
  "workflow_instance_id": "string",
  "key": "string",
  "status": "pending|processing|completed|failed"
}
```

## API Endpoints

### Upload Operations

- `POST /{key}` - Create multipart upload
- `PUT /{key}/{uploadId}/{partNumber}` - Upload a part
- `POST /{key}/{uploadId}` - Complete multipart upload (triggers workflow and database storage)
- `DELETE /{key}/{uploadId}` - Abort multipart upload

### Status and Monitoring

- `GET /{key}/{uploadId}` - Get upload status and workflow information
- `GET /uploads` - List all uploads (with pagination support)
- `GET /` - Upload interface (HTML page)

## Upload Status Flow

1. **pending** - Upload created, database record created (workflow_instance_id is empty)
2. **processing** - Upload completed, workflow started, workflow_instance_id populated
3. **completed** - Workflow completed successfully
4. **failed** - Workflow failed during processing

## Upload Lifecycle

1. **Create Upload** (`POST /{key}`) - Creates multipart upload and database record with pending status
2. **Upload Parts** (`PUT /{key}/{uploadId}/{partNumber}`) - Upload file parts (no database changes)
3. **Complete Upload** (`POST /{key}/{uploadId}`) - Completes upload, starts workflow, updates database with workflow ID and processing status
4. **Workflow Processing** - Background compression, updates status to completed/failed
5. **Abort Upload** (`DELETE /{key}/{uploadId}`) - Aborts upload and removes database record

## Setup Instructions

### 1. Database Setup

Follow the instructions in `scripts/setup-database.md` to:
- Create a D1 database
- Update `wrangler.toml` with your database ID
- Run the migration to create the uploads table

### 2. Environment Configuration

Update `wrangler.toml` with your specific configuration:

```toml
[[d1_databases]]
binding = "DB"
database_name = "upload-db"
database_id = "your-actual-database-id"
```

### 3. Run Database Migration

```bash
npm run db:migrate
```

### 4. Deploy

```bash
npm run deploy
```

## Usage Examples

### Upload a file and track progress

1. Create upload (creates database record with pending status):
```bash
curl -X POST https://your-domain.com/api/upload/my-file.txt
# Response: {"key":"my-file.txt","uploadId":"abc123"}
```

2. Check initial status (should show pending):
```bash
curl https://your-domain.com/api/upload/my-file.txt/abc123
# Response: {"uploadId":"abc123","workflowInstanceId":"","key":"my-file.txt","status":"pending",...}
```

3. Upload parts (repeat for each part):
```bash
curl -X PUT https://your-domain.com/api/upload/my-file.txt/abc123/1 \
  --data-binary @part1.bin
```

4. Complete upload (starts workflow and updates status to processing):
```bash
curl -X POST https://your-domain.com/api/upload/my-file.txt/abc123 \
  -H "Content-Type: application/json" \
  -d '{"parts":[{"partNumber":1,"etag":"..."}]}'
```

5. Check status (should show processing with workflow ID):
```bash
curl https://your-domain.com/api/upload/my-file.txt/abc123
# Response: {"uploadId":"abc123","workflowInstanceId":"workflow-def456","key":"my-file.txt","status":"processing",...}
```

6. Monitor until completion:
```bash
curl https://your-domain.com/api/upload/my-file.txt/abc123
# Eventually: {"uploadId":"abc123","workflowInstanceId":"workflow-def456","key":"my-file.txt","status":"completed",...}
```

### Abort an upload (removes database record)

```bash
curl -X DELETE https://your-domain.com/api/upload/my-file.txt/abc123
# Response: 204 No Content
```

### Monitor all uploads

```bash
curl https://your-domain.com/api/upload/uploads?limit=10&offset=0
```

## Development

### Local Development

```bash
npm install
npm run dev
```

### DevContainer Development (Recommended)

This project includes a complete DevContainer setup with glibc 2.35+ support for modern Node.js native modules.

**Prerequisites:**
- VS Code with Remote-Containers extension
- Docker Desktop

**Quick Start:**
1. Open this project in VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Select "Dev Containers: Reopen in Container"
4. Wait for the container to build and setup to complete

**First-time setup in DevContainer:**
```bash
# Login to Cloudflare
wrangler login

# Verify setup
.devcontainer/verify-setup.sh

# Start development
npm run dev
```

The DevContainer includes:
- Ubuntu 22.04 with glibc 2.35
- Node.js 20 and npm
- Wrangler CLI pre-installed
- Pandoc for document conversion
- s3fs-fuse for R2 integration
- All necessary development tools

See `.devcontainer/README.md` for detailed setup instructions.

## Docker with R2 and Pandoc

The project now includes a comprehensive Docker setup with Cloudflare R2 storage integration and Pandoc document conversion capabilities.

### Quick Start with Docker

1. **Copy environment template**:
```bash
cp .env.example .env
# Edit .env with your R2 credentials
```

2. **Build and run with Docker Compose**:
```bash
docker-compose up --build
```

3. **Or run with Docker directly**:
```bash
# Build the image
docker build -t api-upload .

# Run with R2 integration
docker run -p 8080:8080 \
  -e R2_ACCESS_KEY_ID=your_key \
  -e R2_SECRET_ACCESS_KEY=your_secret \
  -e CLOUDFLARE_ACCOUNT_ID=your_account_id \
  -e R2_BUCKET_NAME=your_bucket \
  --privileged \
  api-upload
```

### Document Conversion Features

- **Pandoc Integration**: Convert between markdown, HTML, PDF, DOCX, and more
- **R2 Storage Access**: Direct file access to your Cloudflare R2 bucket at `/mnt/r2`
- **Batch Processing**: Convert multiple documents with built-in scripts
- **Format Support**: Supports all major document formats via Pandoc

### Available Scripts

- `scripts/convert-docs.sh` - Document conversion utilities
- `scripts/test-integration.sh` - Integration testing for R2 and Pandoc

### Documentation

- `docs/docker-r2-pandoc.md` - Comprehensive guide for R2 and Pandoc integration
- See Docker logs for R2 mount status and conversion operations

## Testing

```bash
# Run application tests
npm test

# Run Docker integration tests
./scripts/test-integration.sh
```
