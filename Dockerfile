# syntax=docker/dockerfile:1

FROM node:20-alpine AS build
WORKDIR /app

COPY container/package.json ./
COPY container/src ./src
COPY container/tsconfig.json ./

RUN npm install
RUN npm run build

# Final stage with Ubuntu base for s3fs-fuse and Pandoc
FROM ubuntu:22.04

RUN apt-get update && apt-get install -y \
    # Node.js
    nodejs \
    npm \
    # s3fs-fuse dependencies
    s3fs \
    fuse \
    # Pandoc
    pandoc \
    # Additional utilities
    curl \
    wget \
    ca-certificates \
    # Clean up
    && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /mnt/r2 /etc/s3fs

COPY --from=build /app/dist /usr/local/app/dist
COPY --from=build /app/node_modules /usr/local/app/node_modules
COPY --from=build /app/package.json /usr/local/app/package.json

COPY container/start.sh /usr/local/bin/start.sh

RUN ls -la /usr/local/app/dist
RUN chmod +x /usr/local/bin/start.sh

WORKDIR /usr/local/app

EXPOSE 8080

ENV PORT="8080"
ENV CLOUDFLARE_ACCOUNT_ID=""
ENV R2_ACCESS_KEY_ID=""
ENV R2_SECRET_ACCESS_KEY=""
ENV R2_BUCKET_NAME=""

CMD ["/usr/local/bin/start.sh"]
