# Docker Integration Summary: Cloudflare R2 + Pandoc

This document summarizes the Docker integration enhancements added to the API Upload project.

## What Was Added

### 1. Enhanced Dockerfile
- **Base Image**: Changed from `scratch` to `ubuntu:22.04` to support system dependencies
- **s3fs-fuse**: Added for Cloudflare R2 storage mounting
- **Pandoc**: Added for universal document conversion
- **Startup Script**: Intelligent script that handles R2 mounting and service startup
- **Environment Variables**: Support for R2 credentials configuration

### 2. Cloudflare R2 Integration
Based on the s3fs-fuse documentation for Cloudflare R2:
- **Endpoint Configuration**: `https://[account-id].r2.cloudflarestorage.com`
- **R2-Specific Options**: `nomixupload`, `endpoint=auto`
- **Automatic Mounting**: R2 bucket mounted at `/mnt/r2` when credentials provided
- **Credential Management**: Secure handling of R2 API keys

### 3. Pandoc Document Conversion
Based on the official Pandoc Docker documentation:
- **Universal Converter**: Support for markdown, HTML, PDF, DOCX, and more
- **Integration Ready**: Pre-installed and tested during container startup
- **R2 Compatible**: Can read/write documents directly from/to mounted R2 storage

### 4. Supporting Files Created

#### Configuration Files
- `docker-compose.yml` - Complete Docker Compose setup with R2 integration
- `.env.example` - Environment variable template for R2 credentials

#### Scripts
- `scripts/convert-docs.sh` - Document conversion utilities with R2 support
- `scripts/test-integration.sh` - Comprehensive integration testing

#### Documentation
- `docs/docker-r2-pandoc.md` - Detailed usage guide and examples
- Updated `README.md` - Added Docker and R2 integration sections

## Key Features

### R2 Storage Access
```bash
# Files in R2 are accessible at /mnt/r2/
ls /mnt/r2/
echo "Hello R2" > /mnt/r2/test.txt
```

### Document Conversion
```bash
# Convert markdown to PDF
pandoc /mnt/r2/document.md -o /mnt/r2/document.pdf

# Convert HTML to DOCX
pandoc /mnt/r2/page.html -o /mnt/r2/document.docx

# Batch conversion
./scripts/convert-docs.sh md-to-pdf
```

### Environment Configuration
```bash
# Required for R2 integration
R2_ACCESS_KEY_ID=your_access_key
R2_SECRET_ACCESS_KEY=your_secret_key
CLOUDFLARE_ACCOUNT_ID=your_account_id
R2_BUCKET_NAME=your_bucket
```

## Usage Examples

### Basic Usage (Go Server Only)
```bash
docker build -t api-upload .
docker run -p 8080:8080 api-upload
```

### With R2 Integration
```bash
docker run -p 8080:8080 \
  -e R2_ACCESS_KEY_ID=your_key \
  -e R2_SECRET_ACCESS_KEY=your_secret \
  -e CLOUDFLARE_ACCOUNT_ID=your_account_id \
  -e R2_BUCKET_NAME=your_bucket \
  --privileged \
  api-upload
```

### With Docker Compose
```bash
# Copy and edit environment file
cp .env.example .env

# Start services
docker-compose up --build
```

## Technical Implementation

### Dockerfile Structure
1. **Build Stage**: Compiles Go application
2. **Runtime Stage**: Ubuntu base with s3fs and Pandoc
3. **Startup Script**: Handles R2 mounting and service startup
4. **Security**: Proper credential handling and file permissions

### R2 Mount Configuration
- **URL**: `https://[account-id].r2.cloudflarestorage.com`
- **Options**: `nomixupload`, `endpoint=auto`, `allow_other`
- **Cache**: Uses `/tmp` for s3fs caching
- **Permissions**: Proper file access controls

### Integration Points
- **Go Application**: Can access R2 files at `/mnt/r2/`
- **Pandoc**: Can convert documents stored in R2
- **Scripts**: Automated batch processing capabilities

## Testing

### Integration Tests
```bash
# Run all tests
./scripts/test-integration.sh

# Test specific components
./scripts/test-integration.sh build
```

### Manual Testing
```bash
# Check R2 mount
docker exec -it container_name mountpoint /mnt/r2

# Test Pandoc
docker exec -it container_name pandoc --version

# Test document conversion
docker exec -it container_name ./scripts/convert-docs.sh check
```

## Security Considerations

1. **Credentials**: Environment variables, never hardcoded
2. **Privileged Mode**: Required for FUSE mounting
3. **File Permissions**: Proper access controls on mounted storage
4. **Network Security**: Secure communication with R2 endpoints

## Performance Notes

1. **Caching**: s3fs uses local caching for better performance
2. **Memory**: Monitor usage with large file operations
3. **Network**: Consider proximity to Cloudflare regions
4. **Concurrency**: Adjust s3fs options for workload

## Next Steps

1. **Production Deployment**: Use with orchestration platforms
2. **Monitoring**: Add health checks and logging
3. **Scaling**: Consider horizontal scaling strategies
4. **Custom Processing**: Extend with additional document processing tools

## Troubleshooting

### Common Issues
1. **R2 Mount Fails**: Check credentials and network connectivity
2. **Permission Denied**: Ensure privileged mode is enabled
3. **Pandoc Errors**: Verify document format compatibility
4. **Performance Issues**: Adjust s3fs cache settings

### Debug Commands
```bash
# Check mount status
df -h | grep r2

# View s3fs logs
dmesg | grep s3fs

# Test connectivity
curl -I https://[account-id].r2.cloudflarestorage.com
```

This integration provides a complete solution for document processing with Cloudflare R2 storage, enabling seamless file operations and format conversions within a containerized environment.
