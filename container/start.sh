#!/bin/bash

set -e

# Mount R2 if credentials are provided
if [ -n "$R2_ACCESS_KEY_ID" ] && [ -n "$R2_SECRET_ACCESS_KEY" ] && [ -n "$CLOUDFLARE_ACCOUNT_ID" ] && [ -n "$R2_BUCKET_NAME" ]; then
	echo "Setting up Cloudflare R2 credentials..."
	echo "$R2_ACCESS_KEY_ID:$R2_SECRET_ACCESS_KEY" > /etc/s3fs/passwd-s3fs
	chmod 600 /etc/s3fs/passwd-s3fs

	echo "Mounting Cloudflare R2 bucket..."
	s3fs "$R2_BUCKET_NAME" /mnt/r2 \
		-o url="https://${CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com" \
		-o nomixupload \
		-o endpoint=auto \
		-o passwd_file=/etc/s3fs/passwd-s3fs \
		-o allow_other \
		-o use_cache=/tmp \
		-o ensure_diskfree=100

	echo "R2 bucket mounted successfully at /mnt/r2"
else
	echo "R2 credentials not provided, skipping R2 mount"
	echo "To enable R2 mounting, set: R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, CLOUDFLARE_ACCOUNT_ID, R2_BUCKET_NAME"
fi

pandoc --version
echo "Pandoc is ready for document conversion"

# Start the Node.js server
echo "Starting TypeScript server..."
cd /usr/local/app
exec node dist/index.js
