import { join, dirname, basename, extname } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const INPUT_PATH = './input';
const OUT_PATH = './output';

const execAsync = promisify(exec);

export async function convert(fileName: string) {
	const filePath = join(INPUT_PATH, fileName);
	const filename = basename(filePath, extname(filePath));
	const extractPath = join(OUT_PATH, filename);
	const extractFile = join(extractPath, filename);

	const { stderr } = await execAsync(`pandoc -t html --extract-media='${extractPath}' '${filePath}' -o ${extractFile}.html`);
	if (stderr) {
		console.warn('Pandoc warning:', stderr);
	}
}
