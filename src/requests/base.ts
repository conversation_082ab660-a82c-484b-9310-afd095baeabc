export abstract class BaseRequest {
	protected constructor() { }

	protected json(body: unknown) {
		return new Response(JSON.stringify(body), {
			headers: {
				'Content-Type': 'application/json;charset=UTF-8',
			}
		});
	}

	protected error(error: Error | string, status = 400) {
		console.error(error);
		const message = error instanceof Error ? error.message : error;
		return new Response(message, { status });
	}
}
