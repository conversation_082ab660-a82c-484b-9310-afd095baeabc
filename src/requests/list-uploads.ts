import { BaseRequest } from './base';
import { UploadRecord } from '../lib/UploadRecord';

export class ListUploadsRequest extends BaseRequest {
	private readonly dbService: UploadRecord;

	constructor(db: D1Database) {
		super();
		this.dbService = new UploadRecord(db);
	}

	async execute({ limit, offset }: { limit: number, offset: number }): Promise<Response> {
		try {
			const uploads = await this.dbService.getAll(limit, offset);
			return this.json({
				uploads,
				pagination: {
					limit,
					offset,
					count: uploads.length
				}
			});
		} catch (error: any) {
			return this.error(error);
		}
	}


}
