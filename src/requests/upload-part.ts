import { UploadRequest } from './upload';

export class UploadPartRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string) {
    super(bucket, key, uploadId);
  }

  async execute(index: string, body: Request['body']): Promise<Response> {
    if (body === null) {
      return this.error("Missing request body");
    }
    const partNumber = parseInt(index);
    if (isNaN(partNumber)) {
      return this.error("Invalid part number");
    }
    try {
      const part = await this.upload.uploadPart(partNumber, body);
      return this.json(part);
    } catch (error: any) {
      return this.error(error);
    }
  }
}
