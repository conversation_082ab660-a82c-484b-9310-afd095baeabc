import { BaseRequest } from './base';
import { UploadRecord } from '../lib/UploadRecord';
import { CreateBody } from '../types';

export class CreateRequest extends BaseRequest {
	private readonly dbService: UploadRecord;

	constructor(private readonly bucket: R2Bucket, db: D1Database) {
		super();
		this.dbService = new UploadRecord(db);
	}

	async execute(key: string, { httpMetadata, customMetadata }: CreateBody = {}): Promise<Response> {
		try {
			const { uploadId } = await this.bucket.createMultipartUpload(key, { httpMetadata, customMetadata });
			await this.dbService.create(uploadId, key);
			return this.json({ key, uploadId });
		} catch (error: any) {
			return this.error(error);
		}
	}
}
