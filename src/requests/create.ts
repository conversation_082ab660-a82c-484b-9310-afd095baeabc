import { BaseRequest } from './base';
import { UploadRecord } from '../lib/UploadRecord';
import { CreateBody } from '../types';

export class CreateRequest extends BaseRequest {
	private readonly dbService: UploadRecord;
	private readonly allowedMimeTypes = [
		'application/pdf',
		'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
	];
	private readonly allowedExtensions = ['.pdf', '.docx'];

	constructor(private readonly bucket: R2Bucket, db: D1Database) {
		super();
		this.dbService = new UploadRecord(db);
	}

	private validateFileType(key: string, httpMetadata?: R2HTTPMetadata): boolean {
		const fileExtension = key.toLowerCase().substring(key.lastIndexOf('.'));
		if (!this.allowedExtensions.includes(fileExtension)) {
			return false;
		}
		if (httpMetadata?.contentType && !this.allowedMimeTypes.includes(httpMetadata.contentType)) {
			return false;
		}
		return true;
	}

	async execute(key: string, { httpMetadata, customMetadata }: CreateBody = {}): Promise<Response> {
		try {
			if (!this.validateFileType(key, httpMetadata)) {
				return this.error("Invalid file type. Only PDF and DOCX files are allowed.", 400);
			}
			const { uploadId } = await this.bucket.createMultipartUpload(key, { httpMetadata, customMetadata });
			await this.dbService.create(uploadId, key);
			return this.json({ key, uploadId });
		} catch (error: any) {
			return this.error(error);
		}
	}
}
