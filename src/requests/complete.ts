import { UploadRequest } from './upload';
import { CompleteBody } from '../types';
import { UploadRecord } from '../lib/UploadRecord';

export class CompleteRequest extends UploadRequest {
	private readonly dbService: UploadRecord;

	constructor(bucket: R2Bucket, db: D1Database, key: string, uploadId: string) {
		super(bucket, key, uploadId);
		this.dbService = new UploadRecord(db);
	}

	async execute(body: CompleteBody | null): Promise<Response> {
		if (body === null) {
			return this.error("Missing or incomplete body");
		}
		try {
			const object = await this.upload.complete(body.parts);

			// Update status to completed since upload is finished
			await this.dbService.update(this.uploadId, 'completed');

			return new Response(null, {
				headers: {
					etag: object.httpEtag
				}
			});
		} catch (error: any) {
			return this.error(error);
		}
	}
}
