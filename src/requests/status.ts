import { BaseRequest } from './base';
import { UploadRecord } from '../lib/UploadRecord';

export class StatusRequest extends BaseRequest {
  private readonly dbService: UploadRecord;

  constructor(db: D1Database) {
    super();
    this.dbService = new UploadRecord(db);
  }

  async execute(uploadId: string): Promise<Response> {
    try {
      const upload = await this.dbService.getByUploadId(uploadId);

      if (!upload) {
        return new Response("Upload not found", { status: 404 });
      }

      return this.json({
        uploadId: upload.properties.upload_id,
        key: upload.properties.key,
        status: upload.properties.status,
        createdAt: upload.created_at,
        updatedAt: upload.updated_at
      });
    } catch (error: any) {
      return this.error(error);
    }
  }
}
