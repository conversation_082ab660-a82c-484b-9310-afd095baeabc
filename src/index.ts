import { Env, CompleteBody, CreateBody } from './types';
import { CreateRequest } from './requests/create';
import { CompleteRequest } from './requests/complete';
import { UploadPartRequest } from './requests/upload-part';
import { AbortRequest } from './requests/abort';
import { StatusRequest } from './requests/status';
import { ListUploadsRequest } from './requests/list-uploads';
import html from './home.html';

export * from './lib/Compressor';

export default {
	async fetch(request, env): Promise<Response> {
		let pathname = new URL(request.url).pathname;

		// Remove base path if configured
		if (env.BASE_PATH) {
			const basePath = env.BASE_PATH.startsWith('/') ? env.BASE_PATH : `/${env.BASE_PATH}`;
			if (pathname.startsWith(basePath)) {
				pathname = pathname.slice(basePath.length);
			} else {
				return new Response("Not Found", { status: 404 });
			}
		}

		const pathParts = pathname.slice(1).split('/').filter(part => part.length > 0);

		if (request.method === "POST") {
			if (pathParts.length === 1) {
				// POST /{key} - Create multipart upload
				const body = await request.json<CreateBody>().catch(() => undefined);
				return await new CreateRequest(env.BUCKET, env.DB).execute(pathParts[0], body);
			}
			if (pathParts.length == 2) {
				// POST /{key}/{uploadId} - Complete multipart upload
				const body = await request.json<CompleteBody>().catch(() => null);
				return new CompleteRequest(env.BUCKET, env.DB, pathParts[0], pathParts[1]).execute(body);
			}
		}
		if (request.method === "PUT") {
			if (pathParts.length == 3) {
				// PUT /{key}/{uploadId}/{partNumber} - Upload a part
				return await new UploadPartRequest(env.BUCKET, pathParts[0], pathParts[1]).execute(pathParts[2], request.body);
			}
		}
		if (request.method === "DELETE" && pathParts.length == 2) {
			// DELETE /{key}/{uploadId} - Abort multipart upload
			return await new AbortRequest(env.BUCKET, env.DB, pathParts[0], pathParts[1]).execute();
		}
		if (request.method === "GET") {
			if (pathParts.length == 1 && pathParts[0] === 'uploads') {
				// GET /uploads?limit=100&offset=0 - List all uploads (for debugging/monitoring)
				// Get query parameters for pagination
				const url = new URL(request.url);
				const limit = Math.min(parseInt(url.searchParams.get('limit') || '100'), 100); // Max 100
				const offset = Math.max(parseInt(url.searchParams.get('offset') || '0'), 0);
				return await new ListUploadsRequest(env.DB).execute({ limit, offset });
			}
			if (pathParts.length == 2) {
				// GET /{key}/{uploadId} - Get upload status
				return await new StatusRequest(env.DB).execute(pathParts[1]);
			}
			if (pathname === '/' || pathname === '/index.html') {
				return new Response(html, {
					headers: {
						"content-type": "text/html;charset=UTF-8",
					},
				});
			}
		}
		return new Response("Not Found", { status: 404 });
	},
} satisfies ExportedHandler<Env>;
