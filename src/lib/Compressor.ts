import { Container } from '@cloudflare/containers';
import { Env } from '../types';

export class Compressor extends Container {

	constructor(ctx: DurableObjectState, env: Env) {
		super(ctx, env, {
			envVars: {
				CLOUDFLARE_ACCOUNT_ID: env.CLOUDFLARE_ACCOUNT_ID,
				R2_ACCESS_KEY_ID: env.R2_ACCESS_KEY_ID,
				R2_SECRET_ACCESS_KEY: env.R2_SECRET_ACCESS_KEY,
				R2_BUCKET_NAME: env.R2_BUCKET_NAME
			},
			defaultPort: 8080,
			enableInternet: false,
			sleepAfter: "6m"
		});
	}

	override onStart(): void {
		console.log('Container started!');
	}

	override onStop(): void {
		console.log('Container stopped!');
	}

	override onError(error: unknown): any {
		console.error('Container error:', error);
		throw error;
	}
}
