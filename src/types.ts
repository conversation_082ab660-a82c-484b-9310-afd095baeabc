import type { Compressor } from "./lib/Compressor";

export interface Env {
	BASE_PATH?: string;
	BUCKET: R2Bucket;
	COMPRESSOR: DurableObjectNamespace<Compressor>;
	DB: D1Database;
	CLOUDFLARE_ACCOUNT_ID: string;
	R2_ACCESS_KEY_ID: string;
	R2_SECRET_ACCESS_KEY: string;
	R2_BUCKET_NAME: string;
}

export interface CompleteBody {
	parts: R2UploadedPart[];
}

export interface CreateBody {
	httpMetadata?: R2HTTPMetadata;
	customMetadata?: Record<string, string>;
}
