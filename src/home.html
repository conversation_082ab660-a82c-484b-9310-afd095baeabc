<!doctype html>
<html>

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>API Upload Test Client</title>
	<style>
		body {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
			max-width: 800px;
			margin: 0 auto;
			padding: 20px;
			background-color: #f5f5f5;
		}
		.container {
			background: white;
			padding: 30px;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0,0,0,0.1);
		}
		h1 {
			color: #333;
			margin-bottom: 30px;
		}
		.upload-section {
			margin-bottom: 30px;
			padding: 20px;
			border: 2px dashed #ddd;
			border-radius: 8px;
			text-align: center;
		}
		.upload-section.dragover {
			border-color: #007bff;
			background-color: #f8f9ff;
		}
		input[type="file"] {
			margin: 10px 0;
		}
		button {
			background: #007bff;
			color: white;
			border: none;
			padding: 10px 20px;
			border-radius: 4px;
			cursor: pointer;
			margin: 5px;
		}
		button:hover {
			background: #0056b3;
		}
		button:disabled {
			background: #ccc;
			cursor: not-allowed;
		}
		.progress {
			width: 100%;
			height: 20px;
			background: #f0f0f0;
			border-radius: 10px;
			overflow: hidden;
			margin: 10px 0;
		}
		.progress-bar {
			height: 100%;
			background: #007bff;
			transition: width 0.3s ease;
		}
		.log {
			background: #f8f9fa;
			border: 1px solid #dee2e6;
			border-radius: 4px;
			padding: 15px;
			margin: 20px 0;
			max-height: 300px;
			overflow-y: auto;
			font-family: monospace;
			font-size: 12px;
		}
		.log-entry {
			margin: 5px 0;
			padding: 2px 0;
		}
		.log-entry.error {
			color: #dc3545;
		}
		.log-entry.success {
			color: #28a745;
		}
		.log-entry.info {
			color: #17a2b8;
		}

	</style>
</head>

<body>
	<div class="container">




		<div class="upload-section" id="uploadSection">
			<h3>📁 Select or Drop File</h3>
			<input type="file" id="fileInput" accept=".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
			<p>Or drag and drop a PDF or Word DOCX file here</p>
			<p style="font-size: 0.9em; color: #888;">Supported formats: PDF (.pdf), Word Document (.docx)</p>

			<div style="margin: 1rem 0; padding: 1rem; border: 1px solid #444; border-radius: 4px;">
				<h4>📋 File Metadata (Optional)</h4>
				<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-bottom: 0.5rem;">
					<input type="text" id="contentType" placeholder="Content Type (auto-filled)" style="padding: 0.5rem;" readonly>
					<input type="text" id="contentLanguage" placeholder="Content Language (e.g., en-US)" style="padding: 0.5rem;">
				</div>
				<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
					<input type="text" id="customKey1" placeholder="Custom Key 1" style="padding: 0.5rem;">
					<input type="text" id="customValue1" placeholder="Custom Value 1" style="padding: 0.5rem;">
				</div>
				<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; margin-top: 0.5rem;">
					<input type="text" id="customKey2" placeholder="Custom Key 2" style="padding: 0.5rem;">
					<input type="text" id="customValue2" placeholder="Custom Value 2" style="padding: 0.5rem;">
				</div>
			</div>

			<div>
				<button id="uploadBtn" disabled>Upload File</button>
				<button id="abortBtn" disabled>Abort Upload</button>
			</div>
		</div>

		<div class="progress" style="display: none;" id="progressContainer">
			<div class="progress-bar" id="progressBar"></div>
		</div>
		<div id="progressText"></div>

		<div class="log" id="log">
			<div class="log-entry info">Ready to upload files...</div>
		</div>
	</div>

	<script>
		class MultipartUploader {
			constructor() {
				this.file = null;
				this.uploadId = null;
				this.currentKey = null;
				this.parts = [];
				this.chunkSize = 5 * 1024 * 1024; // 5MB default
				this.aborted = false;
				this.setupEventListeners();
			}

			generateUUID() {
				return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
					const r = Math.random() * 16 | 0;
					const v = c == 'x' ? r : (r & 0x3 | 0x8);
					return v.toString(16);
				});
			}

			setupEventListeners() {
				const fileInput = document.getElementById('fileInput');
				const uploadBtn = document.getElementById('uploadBtn');
				const abortBtn = document.getElementById('abortBtn');
				const uploadSection = document.getElementById('uploadSection');

				fileInput.addEventListener('change', (e) => {
					this.handleFileSelect(e.target.files[0]);
				});

				uploadBtn.addEventListener('click', () => {
					this.startUpload();
				});

				abortBtn.addEventListener('click', () => {
					this.abortUpload();
				});

				// Drag and drop
				uploadSection.addEventListener('dragover', (e) => {
					e.preventDefault();
					uploadSection.classList.add('dragover');
				});

				uploadSection.addEventListener('dragleave', () => {
					uploadSection.classList.remove('dragover');
				});

				uploadSection.addEventListener('drop', (e) => {
					e.preventDefault();
					uploadSection.classList.remove('dragover');
					const files = e.dataTransfer.files;
					if (files.length > 0) {
						this.handleFileSelect(files[0]);
					}
				});
			}

			handleFileSelect(file) {
				if (!file) {
					return;
				}

				// Validate file type
				const allowedTypes = [
					'application/pdf',
					'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
				];

				const allowedExtensions = ['.pdf', '.docx'];
				const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

				if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
					this.log(`❌ Invalid file type: ${file.name}. Only PDF and Word DOCX files are allowed.`, 'error');
					document.getElementById('uploadBtn').disabled = true;
					this.file = null;
					// Clear the file input
					document.getElementById('fileInput').value = '';
					return;
				}

				this.file = file;
				this.log(`✅ Selected file: ${file.name} (${this.formatBytes(file.size)})`, 'info');

				// Auto-set content type based on file extension
				const contentTypeInput = document.getElementById('contentType');
				if (fileExtension === '.pdf') {
					contentTypeInput.value = 'application/pdf';
				} else if (fileExtension === '.docx') {
					contentTypeInput.value = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
				}

				document.getElementById('uploadBtn').disabled = false;
			}

			collectMetadata() {
				const httpMetadata = {};
				const customMetadata = {};

				// Collect HTTP metadata
				const contentType = document.getElementById('contentType').value.trim();
				const contentLanguage = document.getElementById('contentLanguage').value.trim();

				if (contentType) {
					httpMetadata.contentType = contentType;
				}
				if (contentLanguage) {
					httpMetadata.contentLanguage = contentLanguage;
				}

				// Collect custom metadata
				const customKey1 = document.getElementById('customKey1').value.trim();
				const customValue1 = document.getElementById('customValue1').value.trim();
				const customKey2 = document.getElementById('customKey2').value.trim();
				const customValue2 = document.getElementById('customValue2').value.trim();

				if (customKey1 && customValue1) {
					customMetadata[customKey1] = customValue1;
				}
				if (customKey2 && customValue2) {
					customMetadata[customKey2] = customValue2;
				}

				return { httpMetadata, customMetadata };
			}

			async startUpload() {
				if (!this.file) return;

				this.aborted = false;
				this.parts = [];

				this.currentKey = this.generateUUID();

				document.getElementById('uploadBtn').disabled = true;
				document.getElementById('abortBtn').disabled = false;
				document.getElementById('progressContainer').style.display = 'block';

				try {
					// Step 1: Create multipart upload
					this.log(`Starting multipart upload for key: ${this.currentKey}`, 'info');
					this.log(`File: ${this.file.name} (${this.formatBytes(this.file.size)})`, 'info');

					// Collect metadata from form inputs
					const metadata = this.collectMetadata();
					if (Object.keys(metadata.httpMetadata).length > 0 || Object.keys(metadata.customMetadata).length > 0) {
						this.log(`Including metadata: ${JSON.stringify(metadata)}`, 'info');
					}

					const createResponse = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify(metadata)
					});

					if (!createResponse.ok) {
						throw new Error(`Failed to create upload: ${createResponse.status} ${createResponse.statusText}`);
					}

					const createData = await createResponse.json();
					this.uploadId = createData.uploadId;
					this.log(`Created upload with ID: ${this.uploadId}`, 'success');

					// Step 2: Upload parts
					await this.uploadParts(this.currentKey);

					// Step 3: Complete upload
					if (!this.aborted) {
						await this.completeUpload(this.currentKey);
					}

				} catch (error) {
					this.log(`Upload failed: ${error.message}`, 'error');
				} finally {
					document.getElementById('uploadBtn').disabled = false;
					document.getElementById('abortBtn').disabled = true;
				}
			}

			async uploadParts(key) {
				const totalChunks = Math.ceil(this.file.size / this.chunkSize);
				this.log(`Uploading ${totalChunks} parts...`, 'info');

				for (let i = 0; i < totalChunks; i++) {
					if (this.aborted) {
						throw new Error('Upload aborted');
					}

					const start = i * this.chunkSize;
					const end = Math.min(start + this.chunkSize, this.file.size);
					const chunk = this.file.slice(start, end);
					const partNumber = i + 1;

					this.log(`Uploading part ${partNumber}/${totalChunks} (${this.formatBytes(chunk.size)})`, 'info');

					const response = await fetch(`/api/upload/${encodeURIComponent(key)}/${this.uploadId}/${partNumber}`, {
						method: 'PUT',
						body: chunk
					});

					if (!response.ok) {
						throw new Error(`Failed to upload part ${partNumber}: ${response.status} ${response.statusText}`);
					}

					this.parts.push(await response.json());

					// Update progress
					const progress = (partNumber / totalChunks) * 100;
					this.updateProgress(progress);
					this.log(`Part ${partNumber} uploaded successfully (ETag: ${response.etag})`, 'success');
				}
			}

			async completeUpload(key) {
				this.log('Completing multipart upload...', 'info');

				const response = await fetch(`/api/upload/${encodeURIComponent(key)}/${this.uploadId}`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						parts: this.parts
					})
				});

				if (!response.ok) {
					throw new Error(`Failed to complete upload: ${response.status} ${response.statusText}`);
				}

				const result = await response.json();
				this.log(`Upload completed successfully! Location: ${result.location}`, 'success');
				this.updateProgress(100);
			}

			async abortUpload() {
				if (!this.uploadId || !this.currentKey) return;

				this.aborted = true;
				this.log('Aborting upload...', 'info');

				try {
					const response = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}/${this.uploadId}`, {
						method: 'DELETE'
					});

					if (response.ok) {
						this.log('Upload aborted successfully', 'success');
					} else {
						this.log(`Failed to abort upload: ${response.status} ${response.statusText}`, 'error');
					}
				} catch (error) {
					this.log(`Error aborting upload: ${error.message}`, 'error');
				}

				document.getElementById('abortBtn').disabled = true;
				document.getElementById('uploadBtn').disabled = false;
			}

			updateProgress(percent) {
				const progressBar = document.getElementById('progressBar');
				const progressText = document.getElementById('progressText');

				progressBar.style.width = `${percent}%`;
				progressText.textContent = `${Math.round(percent)}% complete`;
			}

			log(message, type = 'info') {
				const logContainer = document.getElementById('log');
				const entry = document.createElement('div');
				entry.className = `log-entry ${type}`;
				entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
				logContainer.appendChild(entry);
				logContainer.scrollTop = logContainer.scrollHeight;
			}

			formatBytes(bytes) {
				if (bytes === 0) return '0 Bytes';
				const k = 1024;
				const sizes = ['Bytes', 'KB', 'MB', 'GB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
			}
		}

		// Initialize the uploader
		const uploader = new MultipartUploader();
	</script>
</body>

</html>
