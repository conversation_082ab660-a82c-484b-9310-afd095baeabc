<!doctype html>
<html>

<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>API Upload Test Client</title>
	<style>
		body {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
			max-width: 800px;
			margin: 0 auto;
			padding: 20px;
			background-color: #f5f5f5;
		}
		.container {
			background: white;
			padding: 30px;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0,0,0,0.1);
		}
		h1 {
			color: #333;
			margin-bottom: 30px;
		}
		.upload-section {
			margin-bottom: 30px;
			padding: 20px;
			border: 2px dashed #ddd;
			border-radius: 8px;
			text-align: center;
		}
		.upload-section.dragover {
			border-color: #007bff;
			background-color: #f8f9ff;
		}
		input[type="file"] {
			margin: 10px 0;
		}
		button {
			background: #007bff;
			color: white;
			border: none;
			padding: 10px 20px;
			border-radius: 4px;
			cursor: pointer;
			margin: 5px;
		}
		button:hover {
			background: #0056b3;
		}
		button:disabled {
			background: #ccc;
			cursor: not-allowed;
		}
		.progress {
			width: 100%;
			height: 20px;
			background: #f0f0f0;
			border-radius: 10px;
			overflow: hidden;
			margin: 10px 0;
		}
		.progress-bar {
			height: 100%;
			background: #007bff;
			transition: width 0.3s ease;
		}
		.log {
			background: #f8f9fa;
			border: 1px solid #dee2e6;
			border-radius: 4px;
			padding: 15px;
			margin: 20px 0;
			max-height: 300px;
			overflow-y: auto;
			font-family: monospace;
			font-size: 12px;
		}
		.log-entry {
			margin: 5px 0;
			padding: 2px 0;
		}
		.log-entry.error {
			color: #dc3545;
		}
		.log-entry.success {
			color: #28a745;
		}
		.log-entry.info {
			color: #17a2b8;
		}

	</style>
</head>

<body>
	<div class="container">




		<div class="upload-section" id="uploadSection">
			<h3>📁 Select or Drop File</h3>
			<input type="file" id="fileInput" accept=".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
			<p>Or drag and drop a PDF or Word DOCX file here</p>
			<p style="font-size: 0.9em; color: #888;">Supported formats: PDF (.pdf), Word Document (.docx)</p>



			<div>
				<button id="uploadBtn" disabled>Upload File</button>
				<button id="abortBtn" disabled>Abort Upload</button>
			</div>
		</div>

		<div class="progress" style="display: none;" id="progressContainer">
			<div class="progress-bar" id="progressBar"></div>
		</div>
		<div id="progressText"></div>

		<div class="log" id="log">
			<div class="log-entry info">Ready to upload files...</div>
		</div>
	</div>

	<script>
		class MultipartUploader {
			constructor() {
				this.file = null;
				this.uploadId = null;
				this.currentKey = null;
				this.parts = [];
				this.chunkSize = 5 * 1024 * 1024; // 5MB default
				this.aborted = false;
				this.setupEventListeners();
			}

			generateUUID() {
				return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
					const r = Math.random() * 16 | 0;
					const v = c == 'x' ? r : (r & 0x3 | 0x8);
					return v.toString(16);
				});
			}

			setupEventListeners() {
				const fileInput = document.getElementById('fileInput');
				const uploadBtn = document.getElementById('uploadBtn');
				const abortBtn = document.getElementById('abortBtn');
				const uploadSection = document.getElementById('uploadSection');

				fileInput.addEventListener('change', (e) => {
					this.handleFileSelect(e.target.files[0]);
				});

				uploadBtn.addEventListener('click', () => {
					this.startUpload();
				});

				abortBtn.addEventListener('click', () => {
					this.abortUpload();
				});

				// Drag and drop
				uploadSection.addEventListener('dragover', (e) => {
					e.preventDefault();
					uploadSection.classList.add('dragover');
				});

				uploadSection.addEventListener('dragleave', () => {
					uploadSection.classList.remove('dragover');
				});

				uploadSection.addEventListener('drop', (e) => {
					e.preventDefault();
					uploadSection.classList.remove('dragover');
					const files = e.dataTransfer.files;
					if (files.length > 0) {
						this.handleFileSelect(files[0]);
					}
				});
			}

			handleFileSelect(file) {
				if (!file) {
					return;
				}

				// Validate file type
				const allowedTypes = [
					'application/pdf',
					'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
				];

				const allowedExtensions = ['.pdf', '.docx'];
				const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

				if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
					this.log(`❌ Invalid file type: ${file.name}. Only PDF and DOCX files are allowed.`, 'error');
					document.getElementById('uploadBtn').disabled = true;
					this.file = null;
					// Clear the file input
					document.getElementById('fileInput').value = '';
					return;
				}

				this.file = file;
				this.log(`✅ Selected file: ${file.name} (${this.formatBytes(file.size)})`, 'info');

				// Log file metadata that will be automatically included
				this.log(`📄 File info - Name: ${file.name}, Type: ${file.type || 'unknown'}, Size: ${this.formatBytes(file.size)}, Last Modified: ${new Date(file.lastModified).toISOString()}`, 'info');

				document.getElementById('uploadBtn').disabled = false;
			}

			generateFileMetadata() {
				if (!this.file) {
					return { httpMetadata: {}, customMetadata: {} };
				}

				const httpMetadata = {};
				const customMetadata = {};

				// Set HTTP metadata based on file properties
				if (this.file.type) {
					httpMetadata.contentType = this.file.type;
				} else {
					// Fallback based on file extension
					const fileExtension = this.file.name.toLowerCase().substring(this.file.name.lastIndexOf('.'));
					if (fileExtension === '.pdf') {
						httpMetadata.contentType = 'application/pdf';
					} else if (fileExtension === '.docx') {
						httpMetadata.contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
					}
				}

				// Set custom metadata with file information
				customMetadata['original-filename'] = this.file.name;
				customMetadata['file-size'] = this.file.size.toString();
				customMetadata['last-modified'] = new Date(this.file.lastModified).toISOString();
				customMetadata['upload-timestamp'] = new Date().toISOString();

				return { httpMetadata, customMetadata };
			}

			async startUpload() {
				if (!this.file) return;

				this.aborted = false;
				this.parts = [];

				this.currentKey = this.generateUUID();

				document.getElementById('uploadBtn').disabled = true;
				document.getElementById('abortBtn').disabled = false;
				document.getElementById('progressContainer').style.display = 'block';

				try {
					// Step 1: Create multipart upload
					this.log(`Starting multipart upload for key: ${this.currentKey}`, 'info');
					this.log(`File: ${this.file.name} (${this.formatBytes(this.file.size)})`, 'info');

					// Generate metadata from file properties
					const metadata = this.generateFileMetadata();
					this.log(`Including file metadata: ${JSON.stringify(metadata, null, 2)}`, 'info');

					const createResponse = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}`, {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify(metadata)
					});

					if (!createResponse.ok) {
						throw new Error(`Failed to create upload: ${createResponse.status} ${createResponse.statusText}`);
					}

					const createData = await createResponse.json();
					this.uploadId = createData.uploadId;
					this.log(`Created upload with ID: ${this.uploadId}`, 'success');

					// Step 2: Upload parts
					await this.uploadParts(this.currentKey);

					// Step 3: Complete upload
					if (!this.aborted) {
						await this.completeUpload(this.currentKey);
					}

				} catch (error) {
					this.log(`Upload failed: ${error.message}`, 'error');
				} finally {
					document.getElementById('uploadBtn').disabled = false;
					document.getElementById('abortBtn').disabled = true;
				}
			}

			async uploadParts(key) {
				const totalChunks = Math.ceil(this.file.size / this.chunkSize);
				this.log(`Uploading ${totalChunks} parts...`, 'info');

				for (let i = 0; i < totalChunks; i++) {
					if (this.aborted) {
						throw new Error('Upload aborted');
					}

					const start = i * this.chunkSize;
					const end = Math.min(start + this.chunkSize, this.file.size);
					const chunk = this.file.slice(start, end);
					const partNumber = i + 1;

					this.log(`Uploading part ${partNumber}/${totalChunks} (${this.formatBytes(chunk.size)})`, 'info');

					const response = await fetch(`/api/upload/${encodeURIComponent(key)}/${this.uploadId}/${partNumber}`, {
						method: 'PUT',
						body: chunk
					});

					if (!response.ok) {
						throw new Error(`Failed to upload part ${partNumber}: ${response.status} ${response.statusText}`);
					}

					this.parts.push(await response.json());

					// Update progress
					const progress = (partNumber / totalChunks) * 100;
					this.updateProgress(progress);
					this.log(`Part ${partNumber} uploaded successfully (ETag: ${response.etag})`, 'success');
				}
			}

			async completeUpload(key) {
				this.log('Completing multipart upload...', 'info');

				const response = await fetch(`/api/upload/${encodeURIComponent(key)}/${this.uploadId}`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						parts: this.parts
					})
				});

				if (!response.ok) {
					throw new Error(`Failed to complete upload: ${response.status} ${response.statusText}`);
				}

				const result = await response.json();
				this.log(`Upload completed successfully! Location: ${result.location}`, 'success');
				this.updateProgress(100);
			}

			async abortUpload() {
				if (!this.uploadId || !this.currentKey) return;

				this.aborted = true;
				this.log('Aborting upload...', 'info');

				try {
					const response = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}/${this.uploadId}`, {
						method: 'DELETE'
					});

					if (response.ok) {
						this.log('Upload aborted successfully', 'success');
					} else {
						this.log(`Failed to abort upload: ${response.status} ${response.statusText}`, 'error');
					}
				} catch (error) {
					this.log(`Error aborting upload: ${error.message}`, 'error');
				}

				document.getElementById('abortBtn').disabled = true;
				document.getElementById('uploadBtn').disabled = false;
			}

			updateProgress(percent) {
				const progressBar = document.getElementById('progressBar');
				const progressText = document.getElementById('progressText');

				progressBar.style.width = `${percent}%`;
				progressText.textContent = `${Math.round(percent)}% complete`;
			}

			log(message, type = 'info') {
				const logContainer = document.getElementById('log');
				const entry = document.createElement('div');
				entry.className = `log-entry ${type}`;
				entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
				logContainer.appendChild(entry);
				logContainer.scrollTop = logContainer.scrollHeight;
			}

			formatBytes(bytes) {
				if (bytes === 0) return '0 Bytes';
				const k = 1024;
				const sizes = ['Bytes', 'KB', 'MB', 'GB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
			}
		}

		// Initialize the uploader
		const uploader = new MultipartUploader();
	</script>
</body>

</html>
