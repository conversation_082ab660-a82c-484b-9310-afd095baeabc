import { z } from 'zod'
import type { Activity } from '~/server/utils/backend/activitypub/activities'
import { PUBLIC_GROUP } from '~/server/utils/backend/activitypub/activities'
import { NOTE, type Note } from '~/server/utils/backend/activitypub/objects/note'
// import { loadExternalMastodonAccount } from '#shared/mastodon/account'
import { makeGetActorAsId, makeGetObjectAsId } from '~/server/utils/backend/activitypub/activities/handle'
import * as actors from '~/server/utils/backend/activitypub/actors'
import { actorURL } from '~/server/utils/backend/activitypub/actors'
import * as outbox from '~/server/utils/backend/activitypub/actors/outbox'
import * as objects from '~/server/utils/backend/activitypub/objects'
import { qb } from '~/server/utils/backend/database/querybuilder'
import { toMastodonStatusFromObject, toMastodonStatusFromRow } from '~/server/utils/backend/mastodon/status'
import type { mastodon } from '#shared/types'
import type { Handle } from '~/server/utils/backend/utils/parse'
import { parseHandle } from '~/server/utils/backend/utils/parse'
import * as webfinger from '~/server/utils/backend/webfinger'

async function getRemoteStatuses(domain: string, handle: Handle, db: D1Database): Promise<mastodon.v1.Status[]> {
  const acct = `${handle.localPart}@${handle.domain}`
  const link = await webfinger.queryAcctLink(handle.domain!, acct)
  if (link === null) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Not found',
    })
  }

  const actor = await actors.getAndCache(link, db)

  const activities = await outbox.get(actor)

  // const account = await loadExternalMastodonAccount(acct, actor)

  const promises = activities.items.map(async (activity: Activity) => {
    const getObjectAsId = makeGetObjectAsId(activity)
    const getActorAsId = makeGetActorAsId(activity)

    if (activity.type === 'Create') {
      const actorId = getActorAsId()
      const originalObjectId = getObjectAsId()
      const res = await objects.cacheObject(domain, db, activity.object, actorId, originalObjectId, false)
      return toMastodonStatusFromObject(db, res.object as Note, domain)
    }

    if (activity.type === 'Announce') {
      let obj: objects.APObject

      const actorId = getActorAsId()
      const objectId = getObjectAsId()

      const localObject = await objects.getObjectById(db, objectId)
      if (localObject === null) {
        try {
          // Object doesn't exists locally, we'll need to download it.
          const remoteObject = await objects.get<Note>(objectId)

          const res = await objects.cacheObject(domain, db, remoteObject, actorId, objectId, false)
          if (res === null) {
            return null
          }
          obj = res.object
        }
        catch (err) {
          if (err instanceof Error) {
            console.warn(`failed to retrieve object ${objectId}: ${err.message}`)
          }
          return null
        }
      }
      else {
        // Object already exists locally, we can just use it.
        obj = localObject
      }

      return toMastodonStatusFromObject(db, obj as Note, domain)
    }

    // FIXME: support other Activities, like Update.
  })
  return (await Promise.all(promises)).filter((item): item is mastodon.v1.Status => Boolean(item))
}

async function getLocalStatuses(domain: string, db: D1Database, handle: Handle, offset: number, withReplies: boolean, maxId: string): Promise<mastodon.v1.Status[]> {
  const actorId = actorURL(domain, handle.localPart)
  let beforeCdate = ''
  if (maxId) {
    // Client asked to retrieve statuses after the max_id
    // As opposed to Mastodon we don't use incremental ID but UUID, we need
    // to retrieve the cdate of the max_id row and only show the newer statuses.
    const QUERY = 'SELECT cdate FROM outbox_objects WHERE object_id=?'
    const row = await db.prepare(QUERY).bind(maxId).first<{ cdate: string }>()
    console.debug(QUERY, [maxId], row)
    if (row) {
      beforeCdate = row.cdate
    }
  }

  const QUERY = `
  SELECT objects.*,
         actors.id as actor_id,
         actors.cdate as actor_cdate,
         actors.properties as actor_properties,
         outbox_objects.actor_id as publisher_actor_id,
         (SELECT count(*) FROM actor_favourites WHERE actor_favourites.object_id=objects.id) as favourites_count,
         (SELECT count(*) FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id) as reblogs_count,
         (SELECT count(*) FROM actor_replies WHERE actor_replies.in_reply_to_object_id=objects.id) as replies_count,
         (SELECT count(*) > 0 FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id AND actor_reblogs.actor_id=?1) as reblogged,
         (SELECT count(*) > 0 FROM actor_favourites WHERE actor_favourites.object_id=objects.id AND actor_favourites.actor_id=?1) as favourited
FROM outbox_objects
INNER JOIN objects ON objects.id=outbox_objects.object_id
INNER JOIN actors ON actors.id=outbox_objects.actor_id
WHERE objects.type='${NOTE}'
      ${withReplies ? '' : 'AND ' + qb.jsonExtractIsNull('objects.properties', 'inReplyTo')}
      AND outbox_objects.target = '${PUBLIC_GROUP}'
      AND outbox_objects.actor_id = ?1
      ${beforeCdate ? `AND outbox_objects.cdate < '${beforeCdate}'` : ''}
ORDER by outbox_objects.cdate DESC
LIMIT ?2 OFFSET ?3
`
  console.debug(QUERY, [actorId.toString(), offset])
  const DEFAULT_LIMIT = 20
  const { success, error, results } = await db.prepare(QUERY).bind(actorId.toString(), DEFAULT_LIMIT, offset).all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  const out: Array<mastodon.v1.Status> = []
  for (let i = 0, len = results.length; i < len; i++) {
    const status = await toMastodonStatusFromRow(domain, db, results[i])
    if (status !== null) {
      out.push(status)
    }
  }
  return out
}

// https://docs.joinmastodon.org/methods/accounts/#statuses
export default defineEventHandler(async (event) => {
  const domain = getRequestDomain(event)
  const { id } = await getValidatedRouterParams(event, z.object({
    id: z.string(),
  }).parse)
  const { pinned: isPinned, offset, withReplies, max_id: maxId } = await getValidatedQuery(event, z.object({
    pinned: z.boolean().default(false),
    offset: z.number().default(0),
    withReplies: z.boolean().default(false),
    max_id: z.string().default(''),
  }).parse)

  const handle = parseHandle(id)
  if (handle.domain === null || (handle.domain !== null && handle.domain === domain)) {
    if (isPinned) {
      // TODO: pinned statuses are not implemented yet.
      // Stub the endpoint to avoid returning statuses that aren't pinned.
      return []
    }
    // Retrieve the statuses from a local user
    return getLocalStatuses(domain, useEnv().DB, handle, offset, withReplies, maxId)
  }
  else if (handle.domain !== null) {
    if (isPinned) {
      // TODO: pinned statuses are not implemented yet.
      // Stub the endpoint to avoid returning statuses that aren't pinned.
      return []
    }
    // Retrieve the statuses of a remote actor
    return getRemoteStatuses(domain, handle, useEnv().DB)
  }
  else {
    throw createError({
      statusCode: 403,
      statusMessage: '',
    })
  }
})
