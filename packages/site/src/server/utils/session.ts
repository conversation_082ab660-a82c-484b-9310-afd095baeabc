import type { H3Event } from 'h3'
import { getPersonByEmail } from '~/server/utils/backend/activitypub/actors'
import { SESSION_COOKIE } from '~/server/utils/backend/config'

interface UserSession {
  email?: string
}

/**
 * Get the user session from the current request
 */
export async function getUserSession(event: H3Event) {
  const session = await _useSession(event)
  if (session.data.email) {
    return getPersonByEmail(useEnv().DB, session.data.email)
  }
  return null
}

/**
 * Get the user session from the current request, or throw an error if not authenticated
 */
export async function requireUserSession(event: H3Event, { statusCode = 401, message = 'Unauthorized' }: { statusCode?: number, message?: string } = {}) {
  const user = await getUserSession(event)
  if (user) {
    return user
  }
  throw createError({
    statusCode,
    message,
  })
}

/**
 * Set a user session
 */
export async function setUserSession(event: H3Event, data: UserSession) {
  const session = await _useSession(event)
  await session.update(data)
}

/**
 * Clear the user session and removing the session cookie
 */
export async function clearUserSession(event: H3Event) {
  const session = await _useSession(event)
  await session.clear()
}

function _useSession(event: H3Event) {
  return useSession<UserSession>(event, {
    name: SESSION_COOKIE,
    password: useEnv().SESSION_SECRET ?? '',
    maxAge: 60 * 60 * 24 * 30, // 30 days
  })
}
