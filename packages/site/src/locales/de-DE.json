{"a11y": {"loading_page": "Seite wird geladen, bitte warten", "loading_titled_page": "Seite {0} wird geladen, bitte warten", "locale_changed": "Sprache wurde zu {0} g<PERSON><PERSON><PERSON><PERSON>", "locale_changing": "Sprache wird ge<PERSON>ndert, bitte warten", "route_loaded": "Seite {0} geladen"}, "account": {"avatar_description": "{0}'s <PERSON><PERSON>", "blocked_by": "<PERSON> wurdest von diesem Account geblockt", "blocked_domains": "Geblockte Mastodon-Instanzen", "blocked_users": "Gesperrte Accounts", "blocking": "<PERSON><PERSON><PERSON>", "bot": "BOT", "favourites": "<PERSON><PERSON>", "follow": "Folgen", "follow_back": "Zurück folgen", "follow_requested": "Ang<PERSON><PERSON><PERSON>", "followers": "Follower", "followers_count": "{0} Abonnent|{0} Abon<PERSON>en", "following": "<PERSON> folgst", "following_count": "Folgt {0}|Folgt {0}", "follows_you": "Folgt dir", "go_to_profile": "Gehe zum Profil", "joined": "Beigetreten", "moved_title": "hat angegeben, dass dies der neue Account ist:", "muted_users": "Stummgeschaltete Accounts", "muting": "Stummgeschaltet", "mutuals": "Freunde", "pinned": "<PERSON><PERSON><PERSON><PERSON>", "posts": "Beiträge", "posts_count": "{0} Beitrag|{0} Beiträge", "profile_description": "{0}'s <PERSON><PERSON>", "profile_unavailable": "Profil nicht verfügbar", "unblock": "Entblocken", "unfollow": "Entfolgen", "unmute": "Stummschaltung aufheben", "view_other_followers": "Follower aus anderen Instanzen werden möglicherweise nicht angezeigt.", "view_other_following": "Das Folgen von anderen Instanzen wird möglicherweise nicht angezeigt."}, "action": {"apply": "<PERSON><PERSON><PERSON>", "bookmark": "Lesezeichen", "bookmarked": "Gemerkt", "boost": "Teilen", "boost_count": "{0}", "boosted": "Get<PERSON><PERSON>", "clear_upload_failed": "<PERSON><PERSON> beim <PERSON> von <PERSON>", "close": "Schließen", "compose": "Verfassen", "confirm": "Bestätigen", "edit": "<PERSON><PERSON><PERSON>", "enter_app": "<PERSON><PERSON>", "favourite": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "favourite_count": "{0}", "favourited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "more": "<PERSON><PERSON>", "next": "Nächster", "prev": "<PERSON><PERSON><PERSON><PERSON>", "publish": "Veröffentlichen", "reply": "Antworten", "reply_count": "{0}", "reset": "Z<PERSON>ücksetzen", "save": "Speichern", "save_changes": "Änderungen speichern", "sign_in": "Anmelden", "switch_account": "Account we<PERSON><PERSON>n", "vote": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "app_desc_short": "Ein flinker Mastodon Web-Client", "app_logo": "Elk Logo", "app_name": "Elk", "attachment": {"edit_title": "Beschreibung", "remove_label": "<PERSON><PERSON>"}, "command": {"activate": "Aktivieren", "complete": "Vollständig", "compose_desc": "<PERSON><PERSON><PERSON><PERSON> einen neuen Beitrag", "n-people-in-the-past-n-days": "{0} Personen in den vergangenen {1} Tagen", "select_lang": "Sprache auswählen", "sign_in_desc": "Bestehenden Account hinzufügen", "switch_account": "Zu {0} we<PERSON><PERSON>n", "switch_account_desc": "<PERSON>u einem anderen Account wechseln", "toggle_dark_mode": "Farbschema ändern", "toggle_zen_mode": "Zen-<PERSON><PERSON>"}, "common": {"end_of_list": "<PERSON><PERSON> der Liste", "error": "FEHLER", "in": "in", "not_found": "404 - Nicht gefunden", "offline_desc": "Anscheinend bist du offline. Bitte überprüfe deine Netzwerkverbindung."}, "compose": {"draft_title": "Entwurf {0}", "drafts": "Entwürfe ({v})"}, "confirm": {"common": {"cancel": "Abbrechen", "confirm": "OK"}, "delete_posts": {"cancel": "Abbrechen", "confirm": "Löschen", "title": "Möchtest du diesen Beitrag wirklich löschen?"}}, "conversation": {"with": "mit"}, "error": {"account_not_found": "Account {0} nicht gefunden", "explore-list-empty": "<PERSON>an ist nichts in den Trends. Schau einfach später nochmal vorbei!", "file_size_cannot_exceed_n_mb": "Die Dateigröße darf {0} MB nicht überschreiten", "sign_in_error": "Kann nicht mit <PERSON> verbinden", "status_not_found": "Beitrag nicht gefunden", "unsupported_file_format": "Nicht unterstütztes Dateiformat"}, "help": {"desc_highlight": "<PERSON><PERSON><PERSON><PERSON> hier und da einige Bugs und fehlende Funktionen.", "desc_para1": "Danke für dein Interesse an Elk, unser noch in der Bearbeitung befindlicher, generischer Mastodon-Client!", "desc_para2": "Wir arbeiten stetig an der Entwicklung und verbessern ihn mit der Zeit. Und wir laden dich schon sehr bald ein uns zu helfen, sobald wir ihn <PERSON>ffen machen!", "desc_para3": "Doch in der Zwischenzeit kannst du der Entwicklung aushelfen, indem du unsere Teammitglieder durch die unten stehenden Links unterstützt.", "desc_para4": "Elk ist Open Source. \n<PERSON><PERSON> <PERSON> be<PERSON>, <PERSON><PERSON><PERSON> g<PERSON>en oder einen Beitrag leisten möcht<PERSON>,", "desc_para5": "Kontaktiere uns auf GitHub", "desc_para6": "und mache mit.", "title": "Elk ist in der Alpha!"}, "language": {"search": "<PERSON><PERSON>"}, "menu": {"block_account": "<PERSON><PERSON><PERSON> {0}", "block_domain": "Sperre Domain {0}", "copy_link_to_post": "<PERSON> zu diesem Beitrag kopieren", "copy_original_link_to_post": "Link zum Originalbeitrag kopieren", "delete": "Löschen", "delete_and_redraft": "Löschen und neu erstellen", "direct_message_account": "Direktnachr<PERSON>t an {0}", "edit": "<PERSON><PERSON><PERSON>", "hide_reblogs": "<PERSON><PERSON><PERSON> <PERSON> {0} ausble<PERSON>", "mention_account": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0}", "mute_account": "{0} stum<PERSON><PERSON><PERSON>", "mute_conversation": "Diesem Beitrag stumms<PERSON>ten", "open_in_original_site": "Auf Originalseite öffnen", "pin_on_profile": "An <PERSON><PERSON>", "share_post": "<PERSON><PERSON> diesen Beitrag", "show_favourited_and_boosted_by": "<PERSON><PERSON>ge mir, wer favorisiert und geboostet hat", "show_reblogs": "<PERSON><PERSON><PERSON> von {0} anzeigen", "show_untranslated": "Übersetzung schliessen", "toggle_theme": {"dark": "Dunkles Farbschema aktivieren", "light": "Helles Farbschema aktivieren"}, "translate_post": "Beitrag übersetzen", "unblock_account": "Entsperre {0}", "unblock_domain": "Entsperren Domain {0}", "unmute_account": "<PERSON><PERSON>ms<PERSON><PERSON><PERSON> von {0} aufheben", "unmute_conversation": "Stummschaltung aufheben", "unpin_on_profile": "<PERSON>"}, "nav": {"back": "Zurück", "blocked_domains": "Gesperrte Domänen", "blocked_users": "<PERSON><PERSON><PERSON>", "bookmarks": "Lesezeichen", "built_at": "Letzter Build: {0}", "compose": "Verfassen", "conversations": "Direktnachrichten", "explore": "Entdecken", "favourites": "<PERSON><PERSON>", "federated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "home": "Startseite", "list": "Liste", "lists": "Listen", "local": "<PERSON><PERSON>", "muted_users": "Stummgeschaltete Benutzer", "notifications": "Mitteilungen", "profile": "Profil", "search": "<PERSON><PERSON>", "select_feature_flags": "Feature-Flags aktivieren", "select_font_size": "Schriftgröße", "select_language": "Sprache auswählen", "settings": "Einstellungen", "show_intro": "Intro anzeigen", "toggle_theme": "Farbschema wechseln", "zen_mode": "Zen-Modus"}, "notification": {"favourited_post": "hat deinen Beitrag favorisiert", "followed_you": "folgt dir", "followed_you_count": "{n} Account folgt dir|{n} Accounts folgen dir", "missing_type": "Fehlender notification.type:", "reblogged_post": "hat deinen Beitrag geteilt", "reported": "{0} hat {1} gemeldet", "request_to_follow": "möchte dir folgen", "signed_up": "hat sich registriert", "update_status": "hat diesen Beitrag aktualisiert"}, "placeholder": {"content_warning": "<PERSON><PERSON><PERSON><PERSON> hier deine <PERSON>", "default_1": "Was geht dir gerade durch den Kopf?", "reply_to_account": "Antwort an {0}", "replying": "Antworten", "the_thread": "<PERSON> <PERSON><PERSON><PERSON>"}, "pwa": {"dismiss": "Ignorieren", "title": "Neues Elk-Update verfügbar!", "update": "Aktualisieren", "update_available_short": "Elk aktualisieren", "webmanifest": {"canary": {"description": "Ein flinker Mastodon-Webclient (Canary)", "name": "Elk (canary)", "short_name": "Elk (canary)"}, "dev": {"description": "Ein flinker Mastodon-Webclient (dev)", "name": "Elk (dev)", "short_name": "Elk (dev)"}, "preview": {"description": "Ein flinker Mastodon-Webclient (Vorschau)", "name": "Elk (Vorschau)", "short_name": "Elk (Vorschau)"}, "release": {"description": "Ein flinker Mastodon-Webclient", "name": "Elk", "short_name": "Elk"}}}, "search": {"search_desc": "Suche nach Accounts & Hashtags", "search_empty": "Nichts für diese Suchbegriffe gefunden"}, "settings": {"about": {"label": "Info", "meet_the_team": "<PERSON><PERSON> das <PERSON>", "sponsor_action": "Sponsere uns", "sponsor_action_desc": "Um das Team bei der Entwicklung von Elk zu unterstützen", "sponsors": "<PERSON><PERSON><PERSON><PERSON>", "sponsors_body_1": "Elk wird ermöglicht durch das großzügige Sponsoring und die Hilfe von:", "sponsors_body_2": "Und alle Unternehmen und Einzelpersonen, die das Elk Team und die Mitglieder sponsern.", "sponsors_body_3": "<PERSON>n dir die App gefällt, erwäge uns zu sponsern:"}, "account_settings": {"description": "Bearbeite Kontoeinstellungen in der Mastodon-Benutzeroberfläche", "label": "Account Ein<PERSON>ungen"}, "interface": {"color_mode": "Farbschema", "dark_mode": "Du<PERSON>les Farbschema", "default": " (Standard)", "font_size": "Schriftgröße", "label": "Oberfläche", "light_mode": "<PERSON><PERSON>", "system_mode": "System"}, "language": {"display_language": "Anzeigesprache", "label": "<PERSON><PERSON><PERSON>", "translations": {"add": "Hinzufügen", "choose_language": "Sprache wählen", "heading": "Übersetzungen", "hide_specific": "Bestimmte Übersetzungen ausblenden", "remove": "Entfernen"}}, "notifications": {"label": "Benachrichtigungen", "notifications": {"label": "Benachrichtigungseinstellungen"}, "push_notifications": {"alerts": {"favourite": "<PERSON><PERSON>", "follow": "Neue Follower", "mention": "Erwähnungen", "poll": "Umfragen", "reblog": "Reblogge deinen Beitrag", "title": "Welche Benachrichtigungen erhalten?"}, "description": "<PERSON><PERSON><PERSON><PERSON> Benachrichtigungen, auch wenn du Elk nicht verwendest.", "instructions": "<PERSON>er<PERSON><PERSON> nicht, die Änderungen mit der Schaltfläche @:settings.notifications.push_notifications.save_settings zu speichern!", "label": "Einstellungen für Push-Benachrichtigungen", "policy": {"all": "<PERSON>", "followed": "Von Accounts, denen ich folge", "follower": "<PERSON>, die mir folgen", "none": "<PERSON>", "title": "Von wem kann ich Benachrichtigungen erhalten?"}, "save_settings": "Einstellungen speichern", "subscription_error": {"clear_error": "Fehler aufräumen", "permission_denied": "Berechtigung verweigert: Aktiviere Benachrichtigungen im  Browser.", "request_error": "<PERSON><PERSON> des Abonnements ist ein Fehler aufgetreten. Versuche es bitte erneut. Wenn der Fehler weiterhin besteht, melde das Problem bitte dem Elk-Repository.", "title": "Push-Benachrichtigungen konnten nicht abonniert werden", "too_many_registrations": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Browserbeschränkungen kann Elk den Push-Benachrichtigungsdienst nicht für mehrere Konten auf verschiedenen Servern verwenden. \nDu solltest Push-Benachrichtigungen für andere Konten abbestellen und es erneut versuchen."}, "title": "Einstellungen für Push-Benachrichtigungen", "undo_settings": "Änderungen rückgängig machen", "unsubscribe": "Push-Benachrichtigungen deaktivieren", "unsupported": "<PERSON><PERSON> Browser unterstützt keine Push-Benachrichtigungen.", "warning": {"enable_close": "Schließen", "enable_description": "Um Benachrichtigungen zu erhalten, wenn Elk nicht geöffnet ist, aktiviere Push-Benachrichtigungen. \nDu kannst gena<PERSON> s<PERSON>uer<PERSON>, welche Arten von Interaktionen Push-Benachrichtigungen generieren, indem du die Schaltfläche \"@:settings.notifications.show_btn{'\"'} oben aktivierest, sobald sie aktiviert ist.", "enable_description_desktop": "Um Benachrichtigungen zu erhalten, wenn Elk nicht geöffnet ist, aktiviere Push-Benachrichtigungen. \nDu kannst unter „Einstellungen > Benachrichtigungen > Einstellungen für Push-Benachrichtigungen“ g<PERSON><PERSON>uer<PERSON>, welche Arten von Interaktionen Push-Benachrichtigungen generieren, sobald diese aktiviert sind.", "enable_description_mobile": "Du erreichst die Einstellungen auch über das Navigationsmenü „Einstellungen > Benachrichtigungen > Push-Benachrichtigungseinstellungen“.", "enable_description_settings": "Um Benachrichtigungen zu erhalten, wenn Elk nicht geöffnet ist, aktiviere Push-Benachrichtigungen. \nDu kannst gena<PERSON> s<PERSON>uer<PERSON>, welche Arten von Interaktionen Push-Benachrichtigungen auf demselben Bildschirm generieren, sobald du sie aktivierst.", "enable_desktop": "Aktiviere Push-Benachrichtigungen", "enable_title": "Verpasse nie wieder Benachrichtigungen", "re_auth": "Offenbar unterstützt dein Server keine Push-Benachrichtigungen. \nVersuche dich abzumelden und erneut anzumelden. Wenn diese Meldung weiterhin angezeigt wird, wende dich an dein Serveradministrator."}}, "show_btn": "Gehe zu den Benachrichtigungseinstellungen"}, "notifications_settings": "Benachrichtigungen", "preferences": {"enable_autoplay": "Autoplay aktivieren", "github_cards": "GitHub Cards", "hide_boost_count": "Boost-Zähler ausblenden", "hide_favorite_count": "<PERSON><PERSON><PERSON> ausblenden", "hide_follower_count": "<PERSON><PERSON><PERSON> der Follower ausblenden", "hide_translation": "Übersetzungen komplett ausblenden", "hide_username_emojis": "Emojis in Namen ausblenden", "hide_username_emojis_description": "Blendet in der Timeline Emojis in den Namen von Kommentator:innen aus. In deren Profilen sind die Emojis weiterhin zu sehen.", "label": "Einstellungen", "title": "Experimentelle Funktionen", "user_picker": "Benutz<PERSON>us<PERSON><PERSON>", "virtual_scroll": "<PERSON><PERSON><PERSON><PERSON>"}, "profile": {"appearance": {"bio": "<PERSON>ber mich", "description": "Avatar, Benutzername, Profil etc. bearbeiten", "display_name": "Anzeigename", "label": "Erscheinungsbild", "profile_metadata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "profile_metadata_desc": "Du kannst bis zu vier Einträge als Tabelle in deinem Profil anzeigen lassen", "title": "<PERSON><PERSON>"}, "featured_tags": {"description": "Leute können deine öffentlichen Beiträge mit diesen Hashtags ansehen.", "label": "Ausgewählte Hashtags"}, "label": "Profil"}, "select_a_settings": "Einstellung auswählen", "users": {"export": "Benutzer-Token exportieren", "import": "Benutzer-Token importieren", "label": "Eingelog<PERSON><PERSON>"}}, "share-target": {"description": "Elk kann so konfigu<PERSON>t werden, dass du Inhalte aus anderen Anwendungen teilen kannst, installiere einfach Elk auf deinem Gerät oder Computer und melden dich an.", "hint": "Um Inhalte mit Elk zu teilen, muss Elk installiert sein und du musst angemeldet sein.", "title": "Teile über Elk"}, "state": {"attachments_exceed_server_limit": "Die Anzahl der Anhänge hat das Limit pro Beitrag überschritten.", "attachments_limit_error": "Limit pro Beitrag überschritten", "edited": "(bearbeitet)", "editing": "<PERSON><PERSON><PERSON>", "loading": "Laden...", "publishing": "Veröffentlichung", "upload_failed": "Upload fehlgeschlagen", "uploading": "Hochladen..."}, "status": {"account": {"suspended_message": "Der Account dieses Status wurde vorübergehend gesperrt.", "suspended_show": "Inhalt trotzdem anzeigen?"}, "boosted_by": "<PERSON><PERSON><PERSON> von", "edited": "Zuletzt bearbeitet: {0}", "favourited_by": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "filter_hidden_phrase": "Versteckt durch", "filter_show_anyway": "Trotzdem zeigen", "img_alt": {"desc": "Beschreibung", "dismiss": "Schließen"}, "poll": {"count": "{0} Stimmen|{0} Stimme|{0} Stimmen", "ends": "Endet: {0}", "finished": "Beendet: {0}"}, "reblogged": "{0} teilte", "replying_to": "Antworten auf {0}", "show_full_thread": "Vollständigen Thread anzeigen", "someone": "<PERSON><PERSON>", "spoiler_show_less": "<PERSON><PERSON><PERSON> we<PERSON>", "spoiler_show_more": "<PERSON><PERSON><PERSON> mehr", "thread": "<PERSON><PERSON><PERSON>", "try_original_site": "Versuche die ursprüngliche Seite"}, "status_history": {"created": "Erstellt: {0}", "edited": "Bearbeitet: {0}"}, "tab": {"for_you": "<PERSON><PERSON><PERSON> dich", "hashtags": "Hashtags", "media": "Medien", "news": "Nachrichten", "notifications_all": "Alle", "notifications_mention": "Erwähnungen", "posts": "Beiträge", "posts_with_replies": "Beiträge und Antworten"}, "tag": {"follow": "Folgen", "follow_label": "Folge dem {0}-Tag", "unfollow": "Entfolgen", "unfollow_label": "Entfolge dem {0}-Tag"}, "time_ago_options": {"day_future": "in 0 Tagen|morgen|in {n} Tagen", "day_past": "vor 0 Tagen|gestern|vor {n} Tagen", "hour_future": "in 0 Stunden|in 1 Stunde|in {n} Stunden", "hour_past": "vor 0 Stunden|vor 1 Stunde|vor {n} Stunden", "just_now": "gerade eben", "minute_future": "in 0 Minuten|in 1 Minute|in {n} Minuten", "minute_past": "vor 0 Minuten|vor 1 Minute|vor {n} Minuten", "month_future": "in 0 Monaten|nä<PERSON><PERSON> Monat|in {n} Monaten", "month_past": "vor 0 Monaten|let<PERSON>ten Monat|vor {n} Monaten", "second_future": "gerade eben|in {n} Sekunde|in {n} Sekunden", "second_past": "gerade eben|vor {n} Sekunde|vor {n} Sekunden", "short_day_future": "in {n}T", "short_day_past": "{n}T", "short_hour_future": "in {n}Std", "short_hour_past": "{n}Std", "short_minute_future": "in {n}Min", "short_minute_past": "{n}Min", "short_month_future": "in {n}Mo", "short_month_past": "{n}Mo", "short_second_future": "in {n}Sek", "short_second_past": "{n}Sek", "short_week_future": "in {n}W", "short_week_past": "{n}W", "short_year_future": "in {n}J", "short_year_past": "{n}J", "week_future": "in 0 Wochen|nächste Woche|in {n} Wochen", "week_past": "vor 0 Wochen|letzte W<PERSON>e|vor {n} Wochen", "year_future": "in 0 Jahren|nächstes Jahr|in {n} Jahren", "year_past": "vor 0 Jahren|letztes Jahren|vor {n} Jahren"}, "timeline": {"show_new_items": "Zeige {v} neue Beiträge|Zeige {v} neuen Beitrag|Zeige {v} neue Beiträge", "view_older_posts": "Ältere Beiträge aus anderen Instanzen werden möglicherweise nicht angezeigt."}, "title": {"federated_timeline": "Föderierte Timeline", "local_timeline": "Lokale Timeline"}, "tooltip": {"add_content_warning": "Inhaltswarnung hinzufügen", "add_emojis": "<PERSON><PERSON><PERSON><PERSON>", "add_media": "Bilder, ein Video oder eine Audiodatei hinzufügen", "add_publishable_content": "Füge Inhalte zum Veröffentlichen hinzu", "change_content_visibility": "Sichtbark<PERSON> von Inhalten ändern", "change_language": "Sprache ändern", "emoji": "<PERSON><PERSON><PERSON>", "explore_links_intro": "Diese Nachrichten werden gerade von Le<PERSON>n auf diesem und anderen Servern des dezentralen Netzwerks besprochen.", "explore_posts_intro": "<PERSON><PERSON> Beiträ<PERSON> von diesem Server gewinnen gerade unter den Leuten von diesem und anderen Servern des dezentralen Netzwerks an Reichweite.", "explore_tags_intro": "Diese Hashtags gewinnen gerade unter den Leuten von diesem und anderen Servern des dezentralen Netzweks an Reichweite.", "toggle_code_block": "Codeblock umschalten"}, "user": {"add_existing": "Bestehendes Konto hinzufügen", "server_address_label": "Mastodon Server <PERSON>", "sign_in_desc": "Melde dich an, um Profilen oder Hashtags zu folgen, Beiträge zu favorisieren, zu teilen und zu beantworten oder von deinem Konto auf einem anderen Server aus zu interagieren.", "sign_in_notice_title": "Anzeigen von {0} öffentlichen Daten", "sign_out_account": "{0} abmelden", "tip_no_account": "<PERSON><PERSON> du noch kein Ma<PERSON>-<PERSON><PERSON> hast, {0}.", "tip_register_account": "wähle einen Server aus und registriere einen"}, "visibility": {"direct": "Nur erwähnte Accounts", "direct_desc": "Nur für erwähnte Accounts sichtbar", "private": "<PERSON><PERSON> Follower", "private_desc": "Nur für Follower sichtbar", "public": "<PERSON><PERSON><PERSON><PERSON>", "public_desc": "<PERSON><PERSON><PERSON> alle sichtbar", "unlisted": "<PERSON>cht gelistet", "unlisted_desc": "<PERSON><PERSON>r alle sichtbar, aber Erkennungsfunktionen deaktiviert"}}