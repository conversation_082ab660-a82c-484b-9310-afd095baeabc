interface UploadProgress {
  completedParts: number
  totalParts: number
  percentage: number
}

interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void
  metadata?: Record<string, string>
  partSize?: number
  maxConcurrentUploads?: number
  retry?: number | false
  retryDelay?: number
}

interface ClientOptions {
  url: string
  metadata?: Record<string, string>
  partSize?: number
  maxConcurrentUploads?: number
  retry?: number | false
  retryDelay?: number
}

interface CreateResponse {
  key: string
  uploadId: string
}

interface UploadPartResponse {
  partNumber: number
  etag: string
}

interface CompleteResponse {
  key: string
  etag: string
  location: string
}

class MultipartUploadClient {
  static async create(key: string, options: ClientOptions) {
    const baseURL = options.url.replace(/\/$/, '')
    const got = $fetch.create({
      baseURL,
      retry: options.retry ?? 3,
      retryDelay: options.retryDelay ?? 1000,
    })

    const { uploadId } = await got<CreateResponse>(`/${key}`, {
      method: 'POST',
      body: { metadata: options.metadata },
    })
    return new MultipartUploadClient(got.create({
      baseURL: `${baseURL}/${key}/${uploadId}`,
    }))
  }

  constructor(private readonly fetch: typeof $fetch) {
  }

  async uploadPart(partNumber: number, body: Blob) {
    return this.fetch<UploadPartResponse>(`/${partNumber}`, {
      method: 'PUT',
      body,
    })
  }

  async complete(parts: UploadPartResponse[]) {
    return this.fetch<CompleteResponse>(`/`, {
      method: 'POST',
      body: { parts: parts.sort((a, b) => a.partNumber - b.partNumber) },
    })
  }

  async abort() {
    await this.fetch(`/`, { method: 'DELETE' })
  }
}

/**
 * Upload a file using multipart upload
 */
export async function uploadFile(file: File | Blob, key: string, { onProgress, ...options }: UploadOptions = {}) {
  const maxConcurrentUploads = options.maxConcurrentUploads || 5

  const partSize = options.partSize || 10 * 1024 * 1024 // Default 10MB parts
  const totalParts = Math.ceil(file.size / partSize)
  const chunks: { number: number, start: number, end: number }[] = []
  for (let i = 0; i < totalParts; i++) {
    const start = i * partSize
    chunks.push({
      number: i + 1,
      start,
      end: Math.min(file.size, start + partSize),
    })
  }

  let client: MultipartUploadClient | undefined

  try {
    client = await MultipartUploadClient.create(key, { url: '/api/upload', ...options })

    const parts: UploadPartResponse[] = []
    for (let i = 0; i < chunks.length; i += maxConcurrentUploads) {
      const batch = chunks.slice(i, i + maxConcurrentUploads)
      await Promise.all(batch.map(async ({ number, start, end }) => {
        parts.push(await client!.uploadPart(number, file.slice(start, end)))
        onProgress?.({
          completedParts: parts.length,
          totalParts,
          percentage: Math.round((parts.length / totalParts) * 100),
        })
      }))
    }

    return await client.complete(parts)
  }
  catch (error) {
    console.error('Multipart upload failed:', error)
    // Attempt to abort the upload on failure
    if (client) {
      try {
        await client.abort()
      }
      catch (abortError) {
        console.error('Failed to abort multipart upload:', abortError)
      }
    }
    throw error
  }
}
