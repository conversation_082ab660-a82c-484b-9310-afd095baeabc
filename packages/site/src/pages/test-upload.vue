<script setup lang="ts">
import { uploadFile } from '~/utils/uploadFile'

defineOptions({
  name: 'TestUpload',
})

definePageMeta({
  name: 'test-upload',
  layout: 'default',
})

// Reactive state
const selectedFile = ref<File | null>(null)
const uploadKey = ref('')
const isUploading = ref(false)
const uploadProgress = ref({
  completedParts: 0,
  totalParts: 0,
  percentage: 0,
})
const uploadResult = ref<any>(null)
const uploadError = ref<string | null>(null)
const logs = ref<string[]>([])

// File input reference
const fileInput = ref<HTMLInputElement>()

// Methods
function onFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    selectedFile.value = file
    uploadKey.value = crypto.randomUUID()
    uploadResult.value = null
    uploadError.value = null
    logs.value = []
    addLog(`Selected file: ${file.name} (${formatFileSize(file.size)})`)
  }
}

function clearFile() {
  selectedFile.value = null
  uploadKey.value = ''
  uploadResult.value = null
  uploadError.value = null
  uploadProgress.value = { completedParts: 0, totalParts: 0, percentage: 0 }
  logs.value = []
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

async function startUpload() {
  if (!selectedFile.value || !uploadKey.value) {
    addLog('Error: No file selected or upload key missing')
    return
  }

  isUploading.value = true
  uploadError.value = null
  uploadResult.value = null
  uploadProgress.value = { completedParts: 0, totalParts: 0, percentage: 0 }

  addLog(`Starting upload of ${selectedFile.value.name} with key: ${uploadKey.value}`)

  try {
    const result = await uploadFile(selectedFile.value, uploadKey.value, {
      onProgress: (progress) => {
        uploadProgress.value = progress
        addLog(`Progress: ${progress.completedParts}/${progress.totalParts} parts (${progress.percentage}%)`)
      },
      metadata: {
        'test-upload': 'true',
        'original-name': selectedFile.value!.name,
      },
      partSize: 5 * 1024 * 1024, // 5MB parts for testing
      maxConcurrentUploads: 3,
      retry: 2,
      retryDelay: 1000,
    })

    uploadResult.value = result
    addLog(`Upload completed successfully!`)
    addLog(`Result: ${JSON.stringify(result, null, 2)}`)
  }
  catch (error) {
    uploadError.value = error instanceof Error ? error.message : 'Unknown error occurred'
    addLog(`Upload failed: ${uploadError.value}`)
    console.error('Upload error:', error)
  }
  finally {
    isUploading.value = false
  }
}

function addLog(message: string) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function copyToClipboard(text: string) {
  navigator.clipboard.writeText(text).then(() => {
    addLog('Copied to clipboard')
  }).catch(() => {
    addLog('Failed to copy to clipboard')
  })
}
</script>

<template>
  <div class="test-upload-page">
    <div class="container">
      <div class="upload-section">
        <!-- File Selection -->
        <div class="file-input-section">
          <label for="file-input" class="file-label">
            Select File to Upload:
          </label>
          <input
            id="file-input"
            ref="fileInput"
            type="file"
            :disabled="isUploading"
            class="file-input"
            @change="onFileSelect">
          <button v-if="selectedFile" :disabled="isUploading" class="btn btn-secondary" @click="clearFile">
            Clear
          </button>
        </div>

        <!-- Upload Key -->
        <div v-if="selectedFile" class="upload-key-section">
          <label for="upload-key" class="upload-key-label">
            Upload Key:
          </label>
          <input
            id="upload-key"
            v-model="uploadKey"
            type="text"
            :disabled="isUploading"
            class="upload-key-input"
            placeholder="Enter upload key (e.g., test-uploads/my-file.jpg)">
        </div>

        <!-- File Info -->
        <div v-if="selectedFile" class="file-info">
          <h3>Selected File:</h3>
          <ul>
            <li><strong>Name:</strong> {{ selectedFile.name }}</li>
            <li><strong>Size:</strong> {{ formatFileSize(selectedFile.size) }}</li>
            <li><strong>Type:</strong> {{ selectedFile.type || 'Unknown' }}</li>
            <li><strong>Last Modified:</strong> {{ new Date(selectedFile.lastModified).toLocaleString() }}</li>
          </ul>
        </div>

        <!-- Upload Controls -->
        <div v-if="selectedFile && uploadKey" class="upload-controls">
          <button :disabled="isUploading" class="btn btn-primary" @click="startUpload">
            {{ isUploading ? 'Uploading...' : 'Start Upload' }}
          </button>
        </div>

        <!-- Progress -->
        <div v-if="isUploading || uploadProgress.percentage > 0" class="progress-section">
          <h3>Upload Progress:</h3>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: uploadProgress.percentage + '%' }" />
          </div>
          <p>
            {{ uploadProgress.completedParts }} / {{ uploadProgress.totalParts }} parts
            ({{ uploadProgress.percentage }}%)
          </p>
        </div>

        <!-- Results -->
        <div v-if="uploadResult" class="result-section success">
          <h3>Upload Successful!</h3>
          <div class="result-content">
            <pre>{{ JSON.stringify(uploadResult, null, 2) }}</pre>
            <button class="btn btn-secondary btn-small" @click="copyToClipboard(JSON.stringify(uploadResult, null, 2))">
              Copy Result
            </button>
          </div>
        </div>

        <div v-if="uploadError" class="result-section error">
          <h3>Upload Failed!</h3>
          <p>{{ uploadError }}</p>
        </div>

        <!-- Logs -->
        <div v-if="logs.length > 0" class="logs-section">
          <h3>Upload Logs:</h3>
          <div class="logs-container">
            <div v-for="(log, index) in logs" :key="index" class="log-entry">
              {{ log }}
            </div>
          </div>
          <button class="btn btn-secondary btn-small" @click="copyToClipboard(logs.join('\n'))">
            Copy Logs
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.test-upload-page {
  min-height: 100vh;
  background: var(--c-bg-base);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.upload-section {
  background: var(--c-bg-soft);
  border-radius: var(--border-radius-large);
  padding: 2rem;
  margin-bottom: 2rem;
}

.info-section {
  background: var(--c-bg-base);
  border: 1px solid var(--c-border);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.info-section ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.info-section li {
  margin: 0.5rem 0;
  color: var(--c-text-base);
}

.file-input-section {
  margin: 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.file-label {
  font-weight: 600;
  color: var(--c-text-base);
}

.file-input {
  flex: 1;
  min-width: 200px;
  padding: 0.5rem;
  border: 2px solid var(--c-border);
  border-radius: var(--border-radius);
  background: var(--c-bg-base);
  color: var(--c-text-base);
}

.upload-key-section {
  margin: 1.5rem 0;
}

.upload-key-label {
  display: block;
  font-weight: 600;
  color: var(--c-text-base);
  margin-bottom: 0.5rem;
}

.upload-key-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--c-border);
  border-radius: var(--border-radius);
  background: var(--c-bg-base);
  color: var(--c-text-base);
  font-family: monospace;
}

.file-info {
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--c-bg-base);
  border-radius: var(--border-radius);
  border: 1px solid var(--c-border);
}

.file-info ul {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
}

.file-info li {
  padding: 0.25rem 0;
  color: var(--c-text-base);
}

.upload-controls {
  margin: 1.5rem 0;
}

.progress-section {
  margin: 1.5rem 0;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: var(--c-bg-base);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--c-border);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--c-primary), var(--c-primary-light));
  transition: width 0.3s ease;
}

.result-section {
  margin: 1.5rem 0;
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 2px solid;
}

.result-section.success {
  border-color: var(--c-success);
  background: var(--c-success-soft);
}

.result-section.error {
  border-color: var(--c-danger);
  background: var(--c-danger-soft);
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.result-content pre {
  background: var(--c-bg-base);
  padding: 1rem;
  border-radius: var(--border-radius);
  overflow-x: auto;
  font-size: 0.875rem;
  border: 1px solid var(--c-border);
}

.logs-section {
  margin: 1.5rem 0;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: var(--c-bg-base);
  border: 1px solid var(--c-border);
  border-radius: var(--border-radius);
  padding: 1rem;
  font-family: monospace;
  font-size: 0.875rem;
}

.log-entry {
  padding: 0.25rem 0;
  color: var(--c-text-base);
  border-bottom: 1px solid var(--c-border-soft);
}

.log-entry:last-child {
  border-bottom: none;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--c-primary);
  color: var(--c-primary-contrast);
}

.btn-primary:hover:not(:disabled) {
  background: var(--c-primary-dark);
}

.btn-secondary {
  background: var(--c-bg-base);
  color: var(--c-text-base);
  border: 2px solid var(--c-border);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--c-bg-soft);
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

h1,
h2,
h3 {
  color: var(--c-text-base);
  margin-bottom: 1rem;
}

h1 {
  font-size: 1.5rem;
}

h2 {
  font-size: 1.25rem;
}

h3 {
  font-size: 1.125rem;
}

p {
  color: var(--c-text-soft);
  line-height: 1.6;
}
</style>
