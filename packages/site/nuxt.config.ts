import { fileURLToPath } from 'node:url'
import { createResolver } from '@nuxt/kit'

// https://v3.nuxtjs.org/api/configuration/nuxt.config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',

  app: {
    head: {
      link: [
        { rel: 'icon', type: 'image/png', href: '/ico.png' },
      ],
    },
  },

  runtimeConfig: {
    public: {
      domain: 'individuallist.xyz',
      translateApi: 'https://translate.elk.zone/translate',
      webSocketUrl: 'wss://individuallist.xyz',
    },
  },

  routeRules: {
    ...(process.env.API ? { '/api/**': { proxy: `${process.env.API}/api/**` } } : {}),
    ...(process.env.API ? { '/media/**': { proxy: `${process.env.API}/media/**` } } : {}),
    '/.well-known/**': { cors: true },
    '/ap/**': { cors: true },
  },

  modules: [
    '@nuxt/eslint',
    '@vueuse/nuxt',
    'nitro-cloudflare-dev',
  ],

  css: [
    '~/styles/main.scss',
    'tippy.js/dist/tippy.css',
  ],

  srcDir: 'src/',

  eslint: {
    config: {
      stylistic: true,
    },
  },

  alias: {
    '#shared': fileURLToPath(new URL('./src/shared', import.meta.url)),
    '#media': fileURLToPath(new URL('../api-media/src', import.meta.url)),
  },

  imports: {
    dirs: [
      './composables/masto',
      './composables/audio',
    ],
    injectAtEnd: true,
  },

  build: {
    analyze: true,
  },

  typescript: {
    shim: false,
  },

  sourcemap: {
    server: true,
    client: true,
  },

  nitro: {
    preset: 'cloudflare_pages',
  },

  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
        },
      },
    },
  },

  hooks: {
    'vite:extendConfig': function (config, { isServer }) {
      if (isServer) {
        const resolver = createResolver(import.meta.url)
        config.plugins!.unshift({
          name: 'mock',
          enforce: 'pre',
          resolveId(id) {
            if (id.match(/(^|\/)(@tiptap)\//)) {
              return resolver.resolve('./mocks/tiptap.ts')
            }
            if (id.match(/(^|\/)(prosemirror)/)) {
              return resolver.resolve('./mocks/prosemirror.ts')
            }
          },
        })
      }
    },
  },

  devtools: {
    enabled: false,
  },
})
