# Docker with Cloudflare R2 and Pandoc Integration

This document explains how to use the enhanced Dockerfile that includes Cloudflare R2 storage support via s3fs-fuse and Pandoc for document conversion.

## Features Added

### 1. Cloudflare R2 Support via s3fs-fuse
- **s3fs-fuse**: FUSE-based file system backed by Amazon S3 (compatible with Cloudflare R2)
- **Automatic mounting**: R2 bucket is mounted at `/mnt/r2` when credentials are provided
- **Configuration**: Uses Cloudflare R2-specific settings for optimal performance

### 2. Pandoc Document Conversion
- **Universal converter**: Convert between various document formats
- **Ready to use**: Pre-installed and tested during container startup
- **Integration**: Can read/write documents from/to mounted R2 storage

## Environment Variables

Set these environment variables to enable R2 mounting:

```bash
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
R2_BUCKET_NAME=your_bucket_name
```

## Usage Examples

### 1. Basic Container Run

```bash
# Run without R2 (Go server only)
docker build -t api-upload .
docker run -p 8080:8080 api-upload
```

### 2. Run with Cloudflare R2 Integration

```bash
# Build the image
docker build -t api-upload .

# Run with R2 credentials
docker run -p 8080:8080 \
  -e R2_ACCESS_KEY_ID=your_access_key \
  -e R2_SECRET_ACCESS_KEY=your_secret_key \
  -e CLOUDFLARE_ACCOUNT_ID=your_account_id \
  -e R2_BUCKET_NAME=your_bucket \
  --privileged \
  api-upload
```

**Note**: The `--privileged` flag is required for FUSE mounting.

### 3. Docker Compose Example

```yaml
version: '3.8'
services:
  api-upload:
    build: .
    ports:
      - "8080:8080"
    environment:
      - R2_ACCESS_KEY_ID=your_access_key
      - R2_SECRET_ACCESS_KEY=your_secret_key
      - CLOUDFLARE_ACCOUNT_ID=your_account_id
      - R2_BUCKET_NAME=your_bucket
    privileged: true
    volumes:
      - /tmp:/tmp  # Optional: for s3fs cache
```

## Cloudflare R2 Configuration

### Getting R2 Credentials

1. **Create R2 API Token**:
   - Go to Cloudflare Dashboard → R2 Object Storage
   - Click "Manage R2 API tokens"
   - Create a new API token with R2 permissions

2. **Find Account ID**:
   - Available in the right sidebar of your Cloudflare dashboard

3. **Create/Use R2 Bucket**:
   - Create a bucket in R2 Object Storage section

### R2 Mount Configuration

The container automatically configures s3fs with Cloudflare R2-specific settings:

```bash
s3fs "$R2_BUCKET_NAME" /mnt/r2 \
    -o url="https://${CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com" \
    -o nomixupload \
    -o endpoint=auto \
    -o passwd_file=/etc/s3fs/passwd-s3fs \
    -o allow_other \
    -o use_cache=/tmp \
    -o ensure_diskfree=100
```

## Pandoc Usage Examples

### 1. Convert Documents in R2

```bash
# Access the running container
docker exec -it <container_id> bash

# Convert markdown to PDF (if R2 is mounted)
pandoc /mnt/r2/document.md -o /mnt/r2/document.pdf

# Convert HTML to DOCX
pandoc /mnt/r2/page.html -o /mnt/r2/document.docx

# Convert with custom options
pandoc /mnt/r2/input.md -o /mnt/r2/output.pdf \
  --pdf-engine=xelatex \
  --variable geometry:margin=1in
```

### 2. Batch Document Processing

```bash
# Convert all markdown files to PDF
for file in /mnt/r2/*.md; do
  pandoc "$file" -o "${file%.md}.pdf"
done

# Convert with metadata
pandoc /mnt/r2/document.md -o /mnt/r2/document.pdf \
  --metadata title="My Document" \
  --metadata author="Your Name"
```

## Integration with Go Application

The Go server can now interact with both R2 storage and Pandoc:

### 1. Reading from R2
```go
// Files in R2 are accessible at /mnt/r2/
file, err := os.Open("/mnt/r2/document.txt")
```

### 2. Using Pandoc for Conversion
```go
// Execute pandoc command
cmd := exec.Command("pandoc", "/mnt/r2/input.md", "-o", "/mnt/r2/output.pdf")
err := cmd.Run()
```

## Troubleshooting

### 1. R2 Mount Issues

```bash
# Check if R2 is mounted
df -h | grep r2

# Check s3fs logs
dmesg | grep s3fs

# Manual mount test
s3fs your-bucket /mnt/r2 -f -d  # foreground with debug
```

### 2. Pandoc Issues

```bash
# Test pandoc installation
pandoc --version

# Test basic conversion
echo "# Test" | pandoc -f markdown -t html
```

### 3. Permission Issues

```bash
# Check mount permissions
ls -la /mnt/r2

# Fix permissions if needed
chmod 755 /mnt/r2
```

## Security Considerations

1. **Credentials**: Never hardcode credentials in the Dockerfile
2. **Privileged Mode**: Required for FUSE, but use with caution
3. **Network**: Ensure secure communication with R2 endpoints
4. **File Permissions**: Properly manage file access in mounted storage

## Performance Optimization

1. **Cache**: s3fs uses `/tmp` for caching by default
2. **Concurrent Operations**: Adjust s3fs options for your workload
3. **Network**: Consider proximity to Cloudflare R2 regions
4. **Memory**: Monitor memory usage with large file operations

## Next Steps

1. **Custom Scripts**: Add your own document processing scripts
2. **API Integration**: Extend the Go server to handle conversion requests
3. **Monitoring**: Add health checks for R2 connectivity
4. **Scaling**: Consider container orchestration for production use
