{
  // These tasks will run in order when initializing your CodeSandbox project.
  "setupTasks": [
    {
      "command": "npm install",
      "name": "Installing Dependencies"
    }
  ],

  // These tasks can be run from CodeSandbox. Running one will open a log in the app.
  "tasks": {
    "predev": {
      "name": "predev",
      "command": "npm run predev",
      "runAtStart": false
    },
    "dev": {
      "name": "dev",
      "command": "npm run dev",
      "runAtStart": true
    },
    "deploy": {
      "name": "deploy",
      "command": "npm run deploy",
      "runAtStart": false
    },
    "postdeploy": {
      "name": "postdeploy",
      "command": "npm run postdeploy",
      "runAtStart": false
    },
    "postdeploy:vars": {
      "name": "postdeploy:vars",
      "command": "npm run postdeploy:vars",
      "runAtStart": false
    },
    "postdeploy:db": {
      "name": "postdeploy:db",
      "command": "npm run postdeploy:db",
      "runAtStart": false
    },
    "test": {
      "name": "test",
      "command": "npm run test",
      "runAtStart": false
    },
    "test:watch": {
      "name": "test:watch",
      "command": "npm run test:watch",
      "runAtStart": false
    },
    "type-check": {
      "name": "type-check",
      "command": "npm run type-check",
      "runAtStart": false
    },
    "db:migrate": {
      "name": "db:migrate",
      "command": "npm run db:migrate",
      "runAtStart": false
    },
    "db:migrate:local": {
      "name": "db:migrate:local",
      "command": "npm run db:migrate:local",
      "runAtStart": false
    }
  }
}
