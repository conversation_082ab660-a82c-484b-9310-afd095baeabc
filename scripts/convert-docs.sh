#!/bin/bash

# Document Conversion Script for R2 + Pandoc Integration
# This script demonstrates how to convert documents stored in Cloudflare R2
# using Pandoc within the Docker container.

set -e

# Configuration
R2_MOUNT_PATH="/mnt/r2"
TEMP_DIR="/tmp/conversions"
LOG_FILE="/tmp/conversion.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if R2 is mounted
check_r2_mount() {
    if ! mountpoint -q "$R2_MOUNT_PATH"; then
        error "R2 is not mounted at $R2_MOUNT_PATH"
        error "Make sure to run the container with proper R2 credentials"
        exit 1
    fi
    success "R2 is mounted at $R2_MOUNT_PATH"
}

# Check if Pandoc is available
check_pandoc() {
    if ! command -v pandoc &> /dev/null; then
        error "Pandoc is not installed"
        exit 1
    fi
    success "Pandoc is available: $(pandoc --version | head -1)"
}

# Create temporary directory
setup_temp_dir() {
    mkdir -p "$TEMP_DIR"
    log "Created temporary directory: $TEMP_DIR"
}

# Convert a single document
convert_document() {
    local input_file="$1"
    local output_file="$2"
    local options="$3"
    
    log "Converting: $input_file -> $output_file"
    
    if [ ! -f "$input_file" ]; then
        error "Input file not found: $input_file"
        return 1
    fi
    
    # Execute pandoc conversion
    if pandoc "$input_file" -o "$output_file" $options; then
        success "Converted: $(basename "$input_file") -> $(basename "$output_file")"
        return 0
    else
        error "Failed to convert: $input_file"
        return 1
    fi
}

# Convert markdown to PDF
md_to_pdf() {
    local input_dir="${1:-$R2_MOUNT_PATH}"
    local output_dir="${2:-$R2_MOUNT_PATH/pdf}"
    
    log "Converting Markdown files to PDF"
    log "Input directory: $input_dir"
    log "Output directory: $output_dir"
    
    mkdir -p "$output_dir"
    
    local count=0
    local success_count=0
    
    for md_file in "$input_dir"/*.md; do
        if [ -f "$md_file" ]; then
            local basename=$(basename "$md_file" .md)
            local pdf_file="$output_dir/${basename}.pdf"
            
            count=$((count + 1))
            if convert_document "$md_file" "$pdf_file" "--pdf-engine=xelatex"; then
                success_count=$((success_count + 1))
            fi
        fi
    done
    
    log "Processed $count markdown files, $success_count successful conversions"
}

# Convert HTML to DOCX
html_to_docx() {
    local input_dir="${1:-$R2_MOUNT_PATH}"
    local output_dir="${2:-$R2_MOUNT_PATH/docx}"
    
    log "Converting HTML files to DOCX"
    log "Input directory: $input_dir"
    log "Output directory: $output_dir"
    
    mkdir -p "$output_dir"
    
    local count=0
    local success_count=0
    
    for html_file in "$input_dir"/*.html; do
        if [ -f "$html_file" ]; then
            local basename=$(basename "$html_file" .html)
            local docx_file="$output_dir/${basename}.docx"
            
            count=$((count + 1))
            if convert_document "$html_file" "$docx_file"; then
                success_count=$((success_count + 1))
            fi
        fi
    done
    
    log "Processed $count HTML files, $success_count successful conversions"
}

# Batch convert with custom format
batch_convert() {
    local input_format="$1"
    local output_format="$2"
    local input_dir="${3:-$R2_MOUNT_PATH}"
    local output_dir="${4:-$R2_MOUNT_PATH/converted}"
    
    log "Batch converting .$input_format files to .$output_format"
    log "Input directory: $input_dir"
    log "Output directory: $output_dir"
    
    mkdir -p "$output_dir"
    
    local count=0
    local success_count=0
    
    for input_file in "$input_dir"/*."$input_format"; do
        if [ -f "$input_file" ]; then
            local basename=$(basename "$input_file" ."$input_format")
            local output_file="$output_dir/${basename}.${output_format}"
            
            count=$((count + 1))
            if convert_document "$input_file" "$output_file"; then
                success_count=$((success_count + 1))
            fi
        fi
    done
    
    log "Processed $count .$input_format files, $success_count successful conversions"
}

# List available documents in R2
list_documents() {
    log "Documents available in R2:"
    
    for ext in md html txt rst tex docx pdf; do
        local files=("$R2_MOUNT_PATH"/*."$ext")
        if [ -e "${files[0]}" ]; then
            echo -e "${GREEN}.$ext files:${NC}"
            ls -la "$R2_MOUNT_PATH"/*."$ext" 2>/dev/null | awk '{print "  " $9 " (" $5 " bytes)"}'
        fi
    done
}

# Show usage information
show_usage() {
    echo "Document Conversion Script for R2 + Pandoc"
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  check           - Check R2 mount and Pandoc availability"
    echo "  list            - List available documents in R2"
    echo "  md-to-pdf       - Convert all Markdown files to PDF"
    echo "  html-to-docx    - Convert all HTML files to DOCX"
    echo "  batch <in> <out> - Batch convert from input format to output format"
    echo "  convert <input> <output> - Convert a single file"
    echo ""
    echo "Examples:"
    echo "  $0 check"
    echo "  $0 list"
    echo "  $0 md-to-pdf"
    echo "  $0 batch md pdf"
    echo "  $0 convert /mnt/r2/doc.md /mnt/r2/doc.pdf"
    echo ""
}

# Main script logic
main() {
    log "Starting document conversion script"
    
    case "${1:-help}" in
        "check")
            check_r2_mount
            check_pandoc
            setup_temp_dir
            ;;
        "list")
            check_r2_mount
            list_documents
            ;;
        "md-to-pdf")
            check_r2_mount
            check_pandoc
            setup_temp_dir
            md_to_pdf "$2" "$3"
            ;;
        "html-to-docx")
            check_r2_mount
            check_pandoc
            setup_temp_dir
            html_to_docx "$2" "$3"
            ;;
        "batch")
            if [ -z "$2" ] || [ -z "$3" ]; then
                error "Batch conversion requires input and output formats"
                echo "Usage: $0 batch <input_format> <output_format> [input_dir] [output_dir]"
                exit 1
            fi
            check_r2_mount
            check_pandoc
            setup_temp_dir
            batch_convert "$2" "$3" "$4" "$5"
            ;;
        "convert")
            if [ -z "$2" ] || [ -z "$3" ]; then
                error "Single file conversion requires input and output files"
                echo "Usage: $0 convert <input_file> <output_file>"
                exit 1
            fi
            check_pandoc
            setup_temp_dir
            convert_document "$2" "$3" "$4"
            ;;
        "help"|*)
            show_usage
            ;;
    esac
    
    log "Script completed"
}

# Run the main function with all arguments
main "$@"
