-- Test script to demonstrate JSON properties functionality in D1
-- Run this with: npx wrangler d1 execute DB --file=./scripts/test-json-properties.sql

-- Insert a test record with JSON properties
INSERT INTO uploads (id, properties, created_at, updated_at) VALUES (
  'test-123',
  json_object(
    'upload_id', 'upload-abc-123',
    'workflow_instance_id', 'workflow-def-456', 
    'key', 'test-file.txt',
    'status', 'pending'
  ),
  datetime('now'),
  datetime('now')
);

-- Query by upload_id using JSON extraction
SELECT 'Query by upload_id:' as test;
SELECT * FROM uploads WHERE json_extract(properties, '$.upload_id') = 'upload-abc-123';

-- Query by workflow_instance_id using JSON extraction  
SELECT 'Query by workflow_instance_id:' as test;
SELECT * FROM uploads WHERE json_extract(properties, '$.workflow_instance_id') = 'workflow-def-456';

-- Query by status using JSON extraction
SELECT 'Query by status:' as test;
SELECT * FROM uploads WHERE json_extract(properties, '$.status') = 'pending';

-- Update status using JSON modification
UPDATE uploads 
SET properties = json_set(properties, '$.status', 'processing'), 
    updated_at = datetime('now')
WHERE json_extract(properties, '$.upload_id') = 'upload-abc-123';

-- Verify the update
SELECT 'After status update:' as test;
SELECT * FROM uploads WHERE json_extract(properties, '$.upload_id') = 'upload-abc-123';

-- Show all properties in a readable format
SELECT 'All properties formatted:' as test;
SELECT 
  id,
  json_extract(properties, '$.upload_id') as upload_id,
  json_extract(properties, '$.workflow_instance_id') as workflow_instance_id,
  json_extract(properties, '$.key') as key,
  json_extract(properties, '$.status') as status,
  created_at,
  updated_at
FROM uploads;

-- Clean up test data
DELETE FROM uploads WHERE id = 'test-123';
