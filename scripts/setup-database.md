# Database Setup Instructions

## 1. Create D1 Database

First, create a new D1 database using Wrang<PERSON>:

```bash
npx wrangler d1 create api-upload-db
```

This will output something like:
```
✅ Successfully created DB 'api-upload-db' in region EEUR
Created your database using D1's new storage backend. The new storage backend is not yet recommended for production workloads, but backs up your data via point-in-time restore.

[[d1_databases]]
binding = "DB"
database_name = "api-upload-db"
database_id = "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
```

## 2. Update wrangler.toml

Copy the `database_id` from the output above and update the `wrangler.toml` file:

```toml
[[d1_databases]]
binding = "DB"
database_name = "api-upload-db"
database_id = "your-actual-database-id-here"  # Replace with the actual ID
```

## 3. Run Database Migration

Execute the migration to create the uploads table with JSON properties:

```bash
# Run the migration
npm run db:migrate
```

For local development, also run:

```bash
# Run migration locally
npm run db:migrate:local
```

Or run it directly:

```bash
# Create uploads table with JSON properties
npx wrangler d1 execute api-upload-db --file=./migrations/0001_create_uploads_table.sql
```

## 4. Verify Database Setup

You can verify the table was created by running:

```bash
npx wrangler d1 execute api-upload-db --command="SELECT name FROM sqlite_master WHERE type='table';"
```

## 5. Query Examples

### Check all uploads:
```bash
npx wrangler d1 execute api-upload-db --command="SELECT * FROM uploads;"
```

### Check upload by uploadId:
```bash
npx wrangler d1 execute api-upload-db --command="SELECT * FROM uploads WHERE json_extract(properties, '$.upload_id') = 'your-upload-id';"
```

### Check upload by workflow instance ID:
```bash
npx wrangler d1 execute api-upload-db --command="SELECT * FROM uploads WHERE json_extract(properties, '$.workflow_instance_id') = 'your-workflow-id';"
```

### Check uploads by status:
```bash
npx wrangler d1 execute api-upload-db --command="SELECT * FROM uploads WHERE json_extract(properties, '$.status') = 'completed';"
```
