#!/bin/bash

# Integration Test Script for R2 + Pandoc + Go Server
# This script tests the complete integration of the Docker container

set -e

# Configuration
CONTAINER_NAME="api-upload-test"
IMAGE_NAME="api-upload"
TEST_DIR="/tmp/api-upload-test"
R2_MOUNT_PATH="/mnt/r2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Cleanup function
cleanup() {
    log "Cleaning up test environment..."
    docker stop "$CONTAINER_NAME" 2>/dev/null || true
    docker rm "$CONTAINER_NAME" 2>/dev/null || true
    rm -rf "$TEST_DIR" 2>/dev/null || true
}

# Setup test environment
setup_test_env() {
    log "Setting up test environment..."
    
    # Create test directory
    mkdir -p "$TEST_DIR"
    
    # Create test documents
    cat > "$TEST_DIR/test.md" << 'EOF'
# Test Document

This is a **test document** for Pandoc conversion.

## Features

- Markdown to PDF conversion
- HTML to DOCX conversion
- Integration with Cloudflare R2

## Code Example

```bash
echo "Hello, World!"
```

> This is a blockquote for testing.
EOF

    cat > "$TEST_DIR/test.html" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Test HTML Document</title>
</head>
<body>
    <h1>Test HTML Document</h1>
    <p>This is a <strong>test HTML document</strong> for conversion.</p>
    <ul>
        <li>HTML to DOCX conversion</li>
        <li>Integration testing</li>
        <li>Cloudflare R2 storage</li>
    </ul>
</body>
</html>
EOF

    success "Test environment created at $TEST_DIR"
}

# Build Docker image
build_image() {
    log "Building Docker image..."
    
    if docker build -t "$IMAGE_NAME" .; then
        success "Docker image built successfully"
    else
        error "Failed to build Docker image"
        exit 1
    fi
}

# Test without R2 (basic functionality)
test_basic_functionality() {
    log "Testing basic functionality (without R2)..."
    
    # Start container without R2 credentials
    docker run -d --name "$CONTAINER_NAME" -p 8080:8080 "$IMAGE_NAME"
    
    # Wait for container to start
    sleep 10
    
    # Test if server is responding
    if curl -f http://localhost:8080/ >/dev/null 2>&1; then
        success "Go server is responding"
    else
        error "Go server is not responding"
        docker logs "$CONTAINER_NAME"
        return 1
    fi
    
    # Test Pandoc availability
    if docker exec "$CONTAINER_NAME" pandoc --version >/dev/null 2>&1; then
        success "Pandoc is available in container"
    else
        error "Pandoc is not available in container"
        return 1
    fi
    
    # Stop container
    docker stop "$CONTAINER_NAME"
    docker rm "$CONTAINER_NAME"
}

# Test with R2 credentials (if provided)
test_r2_integration() {
    log "Testing R2 integration..."
    
    # Check if R2 credentials are available
    if [ -z "$R2_ACCESS_KEY_ID" ] || [ -z "$R2_SECRET_ACCESS_KEY" ] || [ -z "$CLOUDFLARE_ACCOUNT_ID" ] || [ -z "$R2_BUCKET_NAME" ]; then
        warning "R2 credentials not provided, skipping R2 integration test"
        warning "To test R2 integration, set: R2_ACCESS_KEY_ID, R2_SECRET_ACCESS_KEY, CLOUDFLARE_ACCOUNT_ID, R2_BUCKET_NAME"
        return 0
    fi
    
    log "Starting container with R2 credentials..."
    
    # Start container with R2 credentials
    docker run -d --name "$CONTAINER_NAME" \
        -p 8080:8080 \
        -e R2_ACCESS_KEY_ID="$R2_ACCESS_KEY_ID" \
        -e R2_SECRET_ACCESS_KEY="$R2_SECRET_ACCESS_KEY" \
        -e CLOUDFLARE_ACCOUNT_ID="$CLOUDFLARE_ACCOUNT_ID" \
        -e R2_BUCKET_NAME="$R2_BUCKET_NAME" \
        --privileged \
        "$IMAGE_NAME"
    
    # Wait for container to start and mount R2
    sleep 20
    
    # Test if R2 is mounted
    if docker exec "$CONTAINER_NAME" mountpoint -q "$R2_MOUNT_PATH"; then
        success "R2 is mounted successfully"
    else
        error "R2 is not mounted"
        docker logs "$CONTAINER_NAME"
        return 1
    fi
    
    # Test file operations on R2
    log "Testing file operations on R2..."
    
    # Copy test files to R2
    docker cp "$TEST_DIR/test.md" "$CONTAINER_NAME:$R2_MOUNT_PATH/"
    docker cp "$TEST_DIR/test.html" "$CONTAINER_NAME:$R2_MOUNT_PATH/"
    
    # Test Pandoc conversion with R2 files
    if docker exec "$CONTAINER_NAME" pandoc "$R2_MOUNT_PATH/test.md" -o "$R2_MOUNT_PATH/test.pdf"; then
        success "Pandoc conversion (MD to PDF) successful"
    else
        error "Pandoc conversion (MD to PDF) failed"
        return 1
    fi
    
    if docker exec "$CONTAINER_NAME" pandoc "$R2_MOUNT_PATH/test.html" -o "$R2_MOUNT_PATH/test.docx"; then
        success "Pandoc conversion (HTML to DOCX) successful"
    else
        error "Pandoc conversion (HTML to DOCX) failed"
        return 1
    fi
    
    # List files in R2
    log "Files in R2 bucket:"
    docker exec "$CONTAINER_NAME" ls -la "$R2_MOUNT_PATH/"
    
    # Stop container
    docker stop "$CONTAINER_NAME"
    docker rm "$CONTAINER_NAME"
}

# Test document conversion script
test_conversion_script() {
    log "Testing document conversion script..."
    
    # Start container
    docker run -d --name "$CONTAINER_NAME" -p 8080:8080 "$IMAGE_NAME"
    
    # Wait for container to start
    sleep 10
    
    # Copy test files to container
    docker cp "$TEST_DIR/test.md" "$CONTAINER_NAME:/tmp/"
    docker cp "$TEST_DIR/test.html" "$CONTAINER_NAME:/tmp/"
    
    # Test conversion script
    if docker exec "$CONTAINER_NAME" /scripts/convert-docs.sh check; then
        success "Conversion script check passed"
    else
        error "Conversion script check failed"
        return 1
    fi
    
    # Test single file conversion
    if docker exec "$CONTAINER_NAME" /scripts/convert-docs.sh convert /tmp/test.md /tmp/test.pdf; then
        success "Single file conversion test passed"
    else
        error "Single file conversion test failed"
        return 1
    fi
    
    # Stop container
    docker stop "$CONTAINER_NAME"
    docker rm "$CONTAINER_NAME"
}

# Main test function
run_tests() {
    log "Starting integration tests for API Upload with R2 and Pandoc"
    
    # Setup
    setup_test_env
    
    # Build image
    build_image
    
    # Run tests
    test_basic_functionality
    test_conversion_script
    test_r2_integration
    
    success "All tests completed successfully!"
}

# Show usage
show_usage() {
    echo "Integration Test Script for API Upload with R2 and Pandoc"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  test     - Run all integration tests"
    echo "  build    - Build Docker image only"
    echo "  cleanup  - Clean up test environment"
    echo "  help     - Show this help message"
    echo ""
    echo "Environment Variables (for R2 testing):"
    echo "  R2_ACCESS_KEY_ID     - Your R2 access key ID"
    echo "  R2_SECRET_ACCESS_KEY - Your R2 secret access key"
    echo "  CLOUDFLARE_ACCOUNT_ID        - Your Cloudflare account ID"
    echo "  R2_BUCKET_NAME       - Your R2 bucket name"
    echo ""
}

# Trap cleanup on exit
trap cleanup EXIT

# Main script logic
case "${1:-test}" in
    "test")
        run_tests
        ;;
    "build")
        build_image
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|*)
        show_usage
        ;;
esac
